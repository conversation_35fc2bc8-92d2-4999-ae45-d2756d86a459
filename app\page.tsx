import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Hero Section */}
      <section
        className="relative bg-cover bg-center py-20"
        style={{
          backgroundImage: `linear-gradient(rgba(10, 42, 94, 0.7), rgba(10, 42, 94, 0.7)), url('/Banner.jpg')`,
        }}
      >
        <div className="max-w-4xl mx-auto text-center text-white px-4">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Best Solutions for
            <br />
            Education Research and
            <br />
            Lab Management
          </h1>
          <p className="text-lg mb-8 max-w-2xl mx-auto">
            The MK-academia offers a wide range of high-quality educational, research and Lab Management services by our
            panel of experts
          </p>
          <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white px-8 py-3 rounded-full text-lg font-medium">
            Discover More
          </Button>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="flex flex-col items-center text-center">
            <div className="w-20 h-20 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-full h-full"
              >
                <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2 text-[#0a2a5e]">MEDICAL RESEARCH SUPPORT</h3>
            <Link href="/services/medical-research-support">
              <Button
                variant="outline"
                className="mt-4 border-[#f5a623] text-[#f5a623] hover:bg-[#f5a623] hover:text-white"
              >
                READ MORE
              </Button>
            </Link>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="w-20 h-20 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-full h-full"
              >
                <line x1="18" y1="20" x2="18" y2="10"></line>
                <line x1="12" y1="20" x2="12" y2="4"></line>
                <line x1="6" y1="20" x2="6" y2="14"></line>
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2 text-[#0a2a5e]">NON-MEDICAL RESEARCH SUPPORT</h3>
            <Link href="/services/non-medical-research-support">
              <Button
                variant="outline"
                className="mt-4 border-[#f5a623] text-[#f5a623] hover:bg-[#f5a623] hover:text-white"
              >
                READ MORE
              </Button>
            </Link>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="w-20 h-20 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-full h-full"
              >
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                <line x1="8" y1="21" x2="16" y2="21"></line>
                <line x1="12" y1="17" x2="12" y2="21"></line>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2 text-[#0a2a5e]">BRAIN TUMOR CONSULTATION</h3>
            <Link href="/services/brain-tumor-consultation">
              <Button
                variant="outline"
                className="mt-4 border-[#f5a623] text-[#f5a623] hover:bg-[#f5a623] hover:text-white"
              >
                READ MORE
              </Button>
            </Link>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="w-20 h-20 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-full h-full"
              >
                <path d="M12 2a10 10 0 1 0 10 10H12V2z"></path>
                <path d="M21.18 8.02c-1-2.3-2.85-4.17-5.16-5.18"></path>
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2 text-[#0a2a5e]">LAB MANAGEMENT</h3>
            <Link href="/services">
              <Button
                variant="outline"
                className="mt-4 border-[#f5a623] text-[#f5a623] hover:bg-[#f5a623] hover:text-white"
              >
                READ MORE
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-2xl font-bold mb-4 text-[#0a2a5e]">See the Diffrence</h2>
              <h1 className="text-4xl font-bold mb-6 text-[#0a2a5e]">Why to Choose Us</h1>
              <p className="mb-8 text-gray-700 max-w-4xl">
                We can help you through all stages of research cycle (from topic selection to publishing). We provide
                advise on the research topic selection, study design, sample size calculation, strategies to select
                subject, guidance for literature search related topic, citation/referencing and study proposal and IRB.
              </p>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="text-[#0a2a5e] rounded-full p-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-check-circle"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                      <polyline points="22 4 12 14.01 9 11.01" />
                    </svg>
                  </div>
                  <p className="text-lg text-[#0a2a5e]">Highly Qualified Mentors and Trainers</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="text-[#0a2a5e] rounded-full p-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-check-circle"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                      <polyline points="22 4 12 14.01 9 11.01" />
                    </svg>
                  </div>
                  <p className="text-lg text-[#0a2a5e]">State of the art survey and analytics</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="text-[#0a2a5e] rounded-full p-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-check-circle"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                      <polyline points="22 4 12 14.01 9 11.01" />
                    </svg>
                  </div>
                  <p className="text-lg text-[#0a2a5e]">Fast and responsive service</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="text-[#0a2a5e] rounded-full p-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-check-circle"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                      <polyline points="22 4 12 14.01 9 11.01" />
                    </svg>
                  </div>
                  <p className="text-lg text-[#0a2a5e]">Lean, flexible and innovative research support</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="text-[#0a2a5e] rounded-full p-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="lucide lucide-check-circle"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                      <polyline points="22 4 12 14.01 9 11.01" />
                    </svg>
                  </div>
                  <p className="text-lg text-[#0a2a5e]">Bring rigour and thought to your data-collection needs</p>
                </div>
              </div>
            </div>
            <div className="flex justify-center">
              <Image
                src="/2.jpg"
                alt="Why Choose Us"
                width={500}
                height={300}
                className="rounded-lg shadow-lg object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* What We Can Offer Section */}
      <section className="py-10 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-[#0a2a5e] mb-12">What We Can Offer You</h2>

          {/* Placeholder for the offer cards that would be here */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* These would be filled with actual content */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
<Image
  src="/Research-Support1.jpg"
  alt="Research Support"
  width={400}
  height={200}
  className="w-full h-48 object-cover"
/>
<div className="p-6">
  <h3 className="text-xl font-semibold mb-2">Research Support</h3>
  <p className="text-gray-600">
    Comprehensive research support services for academic and professional needs.
  </p>
</div>
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden">
<Image
  src="/Research-Support3.jpg"
  alt="Educational Services"
  width={400}
  height={200}
  className="w-full h-48 object-cover"
/>
<div className="p-6">
  <h3 className="text-xl font-semibold mb-2">Educational Services</h3>
  <p className="text-gray-600">
    High-quality educational services tailored to your specific requirements.
  </p>
</div>
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden">
<Image
  src="/Research-Support4.jpg"
  alt="Lab Management"
  width={400}
  height={200}
  className="w-full h-48 object-cover"
/>
<div className="p-6">
  <h3 className="text-xl font-semibold mb-2">Lab Management</h3>
  <p className="text-gray-600">
    Efficient lab management solutions to optimize your research operations.
  </p>
</div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
