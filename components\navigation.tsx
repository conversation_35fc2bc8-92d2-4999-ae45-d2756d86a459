"use client"

import Link from "next/link"
import Image from "next/image"
import { ChevronDown, Search, X, Menu } from "lucide-react"
import { useState, useEffect } from "react"

export default function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Handle mobile menu toggle - simplified for better reliability
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(prev => !prev)
  }

  // Handle mobile menu item click
  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false)
  }

  // Close mobile menu on escape key
  useEffect(() => {
    if (!isClient) return

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isMobileMenuOpen) {
        setIsMobileMenuOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isMobileMenuOpen, isClient])

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (!isClient) return

    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [isMobileMenuOpen, isClient])

  return (
    <nav className="bg-white py-3 px-4 shadow-md sticky top-0 z-50 mobile-nav-container">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Image
                src="/mk-logo.jpg"
                alt="MK Academia Logo"
                width={50}
                height={50}
                className="mr-2 sm:mr-3"
              />
              <span className="text-[#0a2a5e] font-semibold text-sm sm:text-base lg:text-lg">
                MK ACADEMIA & LAB
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-6">
            <Link href="/" className="text-[#0a2a5e] font-medium hover:text-[#f5a623] transition-colors">
              Home
            </Link>
            <div className="relative group">
              <Link
                href="/services"
                className="text-[#0a2a5e] font-medium flex items-center group-hover:text-[#f5a623] transition-colors"
              >
                Services <ChevronDown className="ml-1 h-4 w-4" />
              </Link>
              <div className="absolute left-0 mt-0 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10 border border-gray-200">
                <div className="py-2">
                  <Link
                    href="/services"
                    className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                  >
                    All Services
                  </Link>
                  <Link
                    href="/services/medical-research-support"
                    className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                  >
                    Medical Research Support
                  </Link>
                  <Link
                    href="/services/non-medical-research-support"
                    className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                  >
                    Non-Medical Research Support
                  </Link>
                  <Link
                    href="/services/brain-tumor-consultation"
                    className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                  >
                    Brain Tumor Consultation
                  </Link>
                  <Link
                    href="/services/statistical-consultancy"
                    className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                  >
                    Statistical Consultancy
                  </Link>
                  <Link
                    href="/services/survey-administration"
                    className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                  >
                    Survey Administration
                  </Link>
                  <div className="border-t border-gray-200 my-2"></div>
                  <Link
                    href="/services/quote-request"
                    className="block px-4 py-3 text-sm text-[#f5a623] font-medium hover:bg-gray-100 transition-colors"
                  >
                    Request Quote
                  </Link>
                </div>
              </div>
            </div>
            <Link href="/about" className="text-[#0a2a5e] font-medium hover:text-[#f5a623] transition-colors">
              About
            </Link>
            <Link href="/contact" className="text-[#0a2a5e] font-medium hover:text-[#f5a623] transition-colors">
              Contact
            </Link>
            <Link href="/registration" className="text-[#0a2a5e] font-medium hover:text-[#f5a623] transition-colors">
              Registration
            </Link>
            <button className="text-[#0a2a5e] hover:text-[#f5a623] transition-colors p-2" aria-label="Search">
              <Search className="h-5 w-5" />
            </button>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button
              className="text-[#0a2a5e] p-3 hover:text-[#f5a623] transition-colors touch-manipulation"
              aria-label="Toggle mobile menu"
              aria-expanded={isMobileMenuOpen}
              onClick={toggleMobileMenu}
              style={{ minHeight: '44px', minWidth: '44px' }}
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden bg-white border-t border-gray-200 shadow-lg">
            <div className="flex flex-col space-y-1 pt-4 pb-4">
              <Link
                href="/"
                className="text-[#0a2a5e] font-medium py-3 px-4 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                Home
              </Link>
              <Link
                href="/services"
                className="text-[#0a2a5e] font-medium py-3 px-4 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                All Services
              </Link>
              <Link
                href="/services/medical-research-support"
                className="text-[#0a2a5e] text-sm py-3 px-6 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                Medical Research Support
              </Link>
              <Link
                href="/services/non-medical-research-support"
                className="text-[#0a2a5e] text-sm py-3 px-6 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                Non-Medical Research Support
              </Link>
              <Link
                href="/services/brain-tumor-consultation"
                className="text-[#0a2a5e] text-sm py-3 px-6 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                Brain Tumor Consultation
              </Link>
              <Link
                href="/services/statistical-consultancy"
                className="text-[#0a2a5e] text-sm py-3 px-6 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                Statistical Consultancy
              </Link>
              <Link
                href="/services/survey-administration"
                className="text-[#0a2a5e] text-sm py-3 px-6 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                Survey Administration
              </Link>
              <Link
                href="/services/quote-request"
                className="text-[#f5a623] font-medium py-3 px-6 hover:bg-gray-50 transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                Request Quote
              </Link>
              <Link
                href="/about"
                className="text-[#0a2a5e] font-medium py-3 px-4 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                About
              </Link>
              <Link
                href="/contact"
                className="text-[#0a2a5e] font-medium py-3 px-4 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                Contact
              </Link>
              <Link
                href="/registration"
                className="text-[#0a2a5e] font-medium py-3 px-4 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md touch-manipulation"
                onClick={closeMobileMenu}
                style={{ minHeight: '44px' }}
              >
                Registration
              </Link>
              <button
                className="text-[#0a2a5e] font-medium py-3 px-4 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded-md text-left flex items-center touch-manipulation"
                style={{ minHeight: '44px' }}
              >
                <Search className="h-4 w-4 mr-2" />
                Search
              </button>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
