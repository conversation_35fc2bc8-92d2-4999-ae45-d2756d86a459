'use client'

import { useEffect } from 'react'

export default function Contact() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return

    // Load JotForm script with better error handling
    const script = document.createElement('script')
    script.src = 'https://form.jotform.com/jsform/251880761749469'
    script.type = 'text/javascript'
    script.async = true

    // Add error handling
    script.onerror = () => {
      console.error('Failed to load JotForm script')
      const formContainer = document.getElementById('jotform-container')
      if (formContainer) {
        formContainer.innerHTML = `
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
            <h3 class="text-lg font-semibold text-yellow-800 mb-2">Form Loading Issue</h3>
            <p class="text-yellow-700 mb-4">The contact form is temporarily unavailable. Please contact us directly:</p>
            <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800 font-medium">
              <EMAIL>
            </a>
          </div>
        `
      }
    }

    // Find the form container and append the script
    const formContainer = document.getElementById('jotform-container')
    if (formContainer) {
      // Clear any existing content
      formContainer.innerHTML = '<div class="text-center py-4">Loading contact form...</div>'
      document.head.appendChild(script)
    }

    // Cleanup function to remove script when component unmounts
    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script)
      }
    }
  }, [])

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#0a2a5e] text-white py-16">
        <div className="max-w-4xl mx-auto text-center px-4">
          <h1 className="text-4xl font-bold mb-6">Contact Us</h1>
          <p className="text-lg mb-8">Get in touch with our team for any inquiries or to discuss your research needs</p>
        </div>
      </section>

      {/* Contact Content */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-12">
            {/* JotForm Container */}
            <div className="lg:w-2/3">
              <div id="jotform-container">
                {/* JotForm will be loaded here */}
              </div>
            </div>

            {/* Contact Information */}
            <div className="lg:w-1/3">
              <h2 className="text-3xl font-bold text-[#0a2a5e] mb-6">Contact Information</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-[#0a2a5e] mb-2">Email</h3>
                  <p className="text-gray-700">
                    <a href="mailto:<EMAIL>" className="hover:text-[#f5a623]">
                      <EMAIL>
                    </a>
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-[#0a2a5e] mb-2">Phone</h3>
                  <p className="text-gray-700">+****************</p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-[#0a2a5e] mb-2">Address</h3>
                  <p className="text-gray-700">
                    123 Research Avenue
                    <br />
                    Suite 456
                    <br />
                    Academic City, AC 12345
                    <br />
                    United States
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-[#0a2a5e] mb-2">Office Hours</h3>
                  <p className="text-gray-700">
                    Monday - Friday: 9:00 AM - 5:00 PM
                    <br />
                    Saturday - Sunday: Closed
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-[#0a2a5e] mb-2">Follow Us</h3>
                  <div className="flex space-x-4">
                    <a href="#" className="text-[#0a2a5e] hover:text-[#f5a623]">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        className="w-6 h-6"
                      >
                        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                        <rect x="2" y="9" width="4" height="12"></rect>
                        <circle cx="4" cy="4" r="2"></circle>
                      </svg>
                    </a>
                    <a href="#" className="text-[#0a2a5e] hover:text-[#f5a623]">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        className="w-6 h-6"
                      >
                        <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                      </svg>
                    </a>
                    <a href="#" className="text-[#0a2a5e] hover:text-[#f5a623]">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        className="w-6 h-6"
                      >
                        <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                      </svg>
                    </a>
                    <a href="#" className="text-[#0a2a5e] hover:text-[#f5a623]">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        className="w-6 h-6"
                      >
                        <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                        <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                        <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}