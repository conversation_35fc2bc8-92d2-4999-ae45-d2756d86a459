import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>ircle, Users, ClipboardList, BarChart3, Target } from "lucide-react"

export default function SurveyAdministrationPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Hero Section */}
      <section
        className="relative bg-gradient-to-br from-[#0a2a5e] via-[#1e3a8a] to-[#3b82f6] text-white py-20"
        style={{
          backgroundImage: `linear-gradient(rgba(10, 42, 94, 0.8), rgba(59, 130, 246, 0.8)), url('/placeholder.svg?height=600&width=1200')`,
        }}
      >
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-1/2">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">Survey Administration Services</h1>
              <p className="text-xl mb-8 text-blue-100">
                Comprehensive survey design, implementation, and administration services to gather reliable data for
                your research and business needs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white px-8 py-3">Start Your Survey</Button>
                <Button
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-[#0a2a5e] px-8 py-3 bg-transparent"
                >
                  View Examples
                </Button>
              </div>
            </div>
            <div className="lg:w-1/2">
              <div className="relative">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center justify-center">
                    <ClipboardList className="w-16 h-16 text-blue-200" />
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center justify-center">
                    <Users className="w-16 h-16 text-green-300" />
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center justify-center">
                    <BarChart3 className="w-16 h-16 text-yellow-300" />
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center justify-center">
                    <Target className="w-16 h-16 text-purple-300" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#0a2a5e] mb-4">Our Survey Administration Services</h2>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              From survey design to data collection and analysis, we provide end-to-end survey administration services
              to help you gather meaningful insights.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
              <div className="w-12 h-12 bg-[#0a2a5e] rounded-lg flex items-center justify-center mb-4">
                <ClipboardList className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-[#0a2a5e] mb-3">Survey Design & Development</h3>
              <p className="text-gray-700 mb-4">
                Professional survey design with validated instruments and optimal question structures for reliable data
                collection.
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Questionnaire development</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Question validation and testing</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Survey flow optimization</span>
                </li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
              <div className="w-12 h-12 bg-[#0a2a5e] rounded-lg flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-[#0a2a5e] mb-3">Data Collection</h3>
              <p className="text-gray-700 mb-4">
                Multi-channel data collection strategies to reach your target audience and maximize response rates.
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Online survey deployment</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Phone and face-to-face interviews</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Response monitoring and follow-up</span>
                </li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
              <div className="w-12 h-12 bg-[#0a2a5e] rounded-lg flex items-center justify-center mb-4">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-[#0a2a5e] mb-3">Data Analysis & Reporting</h3>
              <p className="text-gray-700 mb-4">
                Comprehensive analysis of survey data with detailed reporting and actionable insights.
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Statistical analysis</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Data visualization</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Executive summary reports</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Survey Types */}
      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-[#0a2a5e] mb-12 text-center">Types of Surveys We Administer</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-2xl font-semibold text-[#0a2a5e] mb-4">Academic & Research Surveys</h3>
              <p className="text-gray-700 mb-6">
                Specialized survey administration for academic research, thesis projects, and scholarly investigations.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Student satisfaction surveys</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Faculty evaluation surveys</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Research participant surveys</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Longitudinal studies</span>
                </li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-2xl font-semibold text-[#0a2a5e] mb-4">Healthcare & Clinical Surveys</h3>
              <p className="text-gray-700 mb-6">
                Specialized survey administration for healthcare research, patient satisfaction, and clinical outcomes.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Patient satisfaction surveys</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Quality of life assessments</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Healthcare provider surveys</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Clinical trial questionnaires</span>
                </li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-2xl font-semibold text-[#0a2a5e] mb-4">Market Research Surveys</h3>
              <p className="text-gray-700 mb-6">
                Professional market research surveys to understand consumer behavior, preferences, and market trends.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Customer satisfaction surveys</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Product feedback surveys</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Brand awareness studies</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Market segmentation surveys</span>
                </li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-2xl font-semibold text-[#0a2a5e] mb-4">Employee & Organizational Surveys</h3>
              <p className="text-gray-700 mb-6">
                Comprehensive organizational surveys to assess employee satisfaction, engagement, and workplace culture.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Employee engagement surveys</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Workplace culture assessments</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Training needs analysis</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Exit interviews</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-[#0a2a5e] mb-12 text-center">Our Survey Administration Process</h2>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                1
              </div>
              <h3 className="text-lg font-semibold text-[#0a2a5e] mb-2">Planning</h3>
              <p className="text-gray-700 text-sm">Define objectives, target audience, and survey methodology.</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                2
              </div>
              <h3 className="text-lg font-semibold text-[#0a2a5e] mb-2">Design</h3>
              <p className="text-gray-700 text-sm">Create and validate survey instruments with expert review.</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                3
              </div>
              <h3 className="text-lg font-semibold text-[#0a2a5e] mb-2">Deploy</h3>
              <p className="text-gray-700 text-sm">Launch survey across multiple channels and platforms.</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                4
              </div>
              <h3 className="text-lg font-semibold text-[#0a2a5e] mb-2">Monitor</h3>
              <p className="text-gray-700 text-sm">Track responses and implement follow-up strategies.</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                5
              </div>
              <h3 className="text-lg font-semibold text-[#0a2a5e] mb-2">Analyze</h3>
              <p className="text-gray-700 text-sm">Analyze data and deliver comprehensive insights report.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-[#0a2a5e] text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Launch Your Survey?</h2>
          <p className="text-xl mb-8 text-blue-100">
            Contact us today to discuss your survey needs and get professional administration services for reliable data
            collection.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/contact">
              <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white px-8 py-3">
                Start Your Survey Project
              </Button>
            </Link>
            <Link href="/services">
              <Button
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-[#0a2a5e] px-8 py-3 bg-transparent"
              >
                View All Services
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
