import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function ServicesPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Top Bar and Navigation are in layout.tsx */}

      {/* Page Content */}
      <div className="flex-1 py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center text-[#0a2a5e] mb-16">Research Lab Services</h1>

          <div className="mt-16">
            <h2 className="text-3xl font-bold text-center text-[#0a2a5e] mb-12">Request Quote</h2>

            <form className="space-y-6">
              <div className="space-y-4">
                <Input type="text" placeholder="Your Name" className="w-full p-3 border border-gray-300 rounded" />
                <Input type="email" placeholder="Your Email" className="w-full p-3 border border-gray-300 rounded" />
                <Input
                  type="tel"
                  placeholder="Contact Number (Optional)"
                  className="w-full p-3 border border-gray-300 rounded"
                />

                <Select>
                  <SelectTrigger className="w-full p-3 border border-gray-300 rounded">
                    <SelectValue placeholder="Research Lab Test" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="histopathology">Histopathology</SelectItem>
                    <SelectItem value="molecular">Molecular Testing</SelectItem>
                    <SelectItem value="immunology">Immunology</SelectItem>
                    <SelectItem value="microbiology">Microbiology</SelectItem>
                    <SelectItem value="biochemistry">Biochemistry</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="mt-6">
                <h3 className="font-semibold mb-4">Histopathology Research Services</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start space-x-2">
                    <Checkbox id="processing-staining" />
                    <Label htmlFor="processing-staining" className="font-normal">
                      Processing To Staining
                    </Label>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox id="processing-block" />
                    <Label htmlFor="processing-block" className="font-normal">
                      Processing To Block
                    </Label>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox id="sectioning" />
                    <Label htmlFor="sectioning" className="font-normal">
                      Sectioning
                    </Label>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox id="he-staining" />
                    <Label htmlFor="he-staining" className="font-normal">
                      H&E Staining
                    </Label>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox id="ihc-staining" />
                    <Label htmlFor="ihc-staining" className="font-normal">
                      IHC Staining
                    </Label>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox id="scanning" />
                    <Label htmlFor="scanning" className="font-normal">
                      Scanning
                    </Label>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox id="scoring-conventional" />
                    <Label htmlFor="scoring-conventional" className="font-normal">
                      Scoring for Conventional Slides
                    </Label>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox id="scoring-tma" />
                    <Label htmlFor="scoring-tma" className="font-normal">
                      Scoring TMA Slides
                    </Label>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox id="detailed-reporting" />
                    <Label htmlFor="detailed-reporting" className="font-normal">
                      Detailed Reporting
                    </Label>
                  </div>

                  <div className="flex items-start space-x-2">
                    <Checkbox id="data-analysis" />
                    <Label htmlFor="data-analysis" className="font-normal">
                      Data Analysis
                    </Label>
                  </div>
                </div>
              </div>

              <Textarea
                placeholder="Briefly describe required Services"
                className="w-full p-3 border border-gray-300 rounded min-h-[120px]"
              />

              <Button type="submit" className="w-full bg-[#f5a623] hover:bg-[#e69816] text-white py-3 rounded">
                Submit Request
              </Button>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
