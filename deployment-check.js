#!/usr/bin/env node

/**
 * Deployment Validation Script for Next.js Static Export
 * This script checks for common deployment issues on shared hosting
 */

const fs = require('fs');
const path = require('path');

const OUTPUT_DIR = './out';
const ISSUES = [];
const WARNINGS = [];

console.log('🔍 Checking Next.js Static Export Deployment...\n');

// Check if output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
    ISSUES.push('❌ Output directory "./out" not found. Run "npm run build" first.');
    process.exit(1);
}

// Check essential files
const essentialFiles = [
    'index.html',
    '404.html',
    '_next/static',
    '.htaccess'
];

console.log('📁 Checking essential files...');
essentialFiles.forEach(file => {
    const filePath = path.join(OUTPUT_DIR, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file} - Found`);
    } else {
        if (file === '.htaccess') {
            WARNINGS.push(`⚠️  ${file} - Missing (required for shared hosting)`);
        } else {
            ISSUES.push(`❌ ${file} - Missing`);
        }
    }
});

// Check HTML pages
console.log('\n📄 Checking HTML pages...');
const expectedPages = [
    'index.html',
    'about.html',
    'contact.html',
    'registration.html',
    'services.html',
    '404.html'
];

expectedPages.forEach(page => {
    const pagePath = path.join(OUTPUT_DIR, page);
    if (fs.existsSync(pagePath)) {
        console.log(`✅ ${page} - Found`);
        
        // Check file size (empty files indicate build issues)
        const stats = fs.statSync(pagePath);
        if (stats.size < 100) {
            WARNINGS.push(`⚠️  ${page} - File is very small (${stats.size} bytes), may be empty`);
        }
    } else {
        ISSUES.push(`❌ ${page} - Missing`);
    }
});

// Check nested service pages
console.log('\n🔧 Checking service pages...');
const servicePages = [
    'services/medical-research-support.html',
    'services/non-medical-research-support.html',
    'services/brain-tumor-consultation.html',
    'services/statistical-consultancy.html',
    'services/survey-administration.html'
];

servicePages.forEach(page => {
    const pagePath = path.join(OUTPUT_DIR, page);
    if (fs.existsSync(pagePath)) {
        console.log(`✅ ${page} - Found`);
    } else {
        WARNINGS.push(`⚠️  ${page} - Missing (may cause 404 errors)`);
    }
});

// Check _next directory structure
console.log('\n⚡ Checking _next assets...');
const nextDir = path.join(OUTPUT_DIR, '_next');
if (fs.existsSync(nextDir)) {
    const staticDir = path.join(nextDir, 'static');
    if (fs.existsSync(staticDir)) {
        console.log('✅ _next/static - Found');
        
        // Check for CSS files
        const cssDir = path.join(staticDir, 'css');
        if (fs.existsSync(cssDir)) {
            const cssFiles = fs.readdirSync(cssDir).filter(f => f.endsWith('.css'));
            console.log(`✅ CSS files - Found ${cssFiles.length} file(s)`);
        } else {
            WARNINGS.push('⚠️  CSS directory missing');
        }
        
        // Check for JS chunks
        const chunksDir = path.join(staticDir, 'chunks');
        if (fs.existsSync(chunksDir)) {
            const jsFiles = fs.readdirSync(chunksDir).filter(f => f.endsWith('.js'));
            console.log(`✅ JS chunks - Found ${jsFiles.length} file(s)`);
        } else {
            ISSUES.push('❌ JS chunks directory missing');
        }
    } else {
        ISSUES.push('❌ _next/static directory missing');
    }
} else {
    ISSUES.push('❌ _next directory missing');
}

// Check for case sensitivity issues
console.log('\n🔤 Checking for potential case sensitivity issues...');
function checkCaseSensitivity(dir) {
    const items = fs.readdirSync(dir);
    const lowerCaseMap = new Map();
    
    items.forEach(item => {
        const lowerCase = item.toLowerCase();
        if (lowerCaseMap.has(lowerCase)) {
            WARNINGS.push(`⚠️  Potential case sensitivity issue: "${lowerCaseMap.get(lowerCase)}" and "${item}" in ${dir}`);
        } else {
            lowerCaseMap.set(lowerCase, item);
        }
    });
}

checkCaseSensitivity(OUTPUT_DIR);

// Check image files
console.log('\n🖼️  Checking image assets...');
const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'];
const images = fs.readdirSync(OUTPUT_DIR).filter(file => 
    imageExtensions.some(ext => file.toLowerCase().endsWith(ext))
);

console.log(`✅ Found ${images.length} image file(s) in root directory`);

// Summary
console.log('\n' + '='.repeat(50));
console.log('📊 DEPLOYMENT CHECK SUMMARY');
console.log('='.repeat(50));

if (ISSUES.length === 0 && WARNINGS.length === 0) {
    console.log('🎉 All checks passed! Your deployment should work correctly.');
} else {
    if (ISSUES.length > 0) {
        console.log('\n❌ CRITICAL ISSUES (must fix):');
        ISSUES.forEach(issue => console.log(issue));
    }
    
    if (WARNINGS.length > 0) {
        console.log('\n⚠️  WARNINGS (recommended to fix):');
        WARNINGS.forEach(warning => console.log(warning));
    }
}

console.log('\n📋 DEPLOYMENT CHECKLIST:');
console.log('1. Upload all files from "./out" directory to your shared hosting');
console.log('2. Ensure .htaccess file is uploaded and working');
console.log('3. Check that your hosting supports Apache mod_rewrite');
console.log('4. Verify file permissions (644 for files, 755 for directories)');
console.log('5. Test all routes after deployment');

if (ISSUES.length > 0) {
    process.exit(1);
} else {
    process.exit(0);
}
