@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #0a2a5e;
  --secondary: #f5a623;
}

body {
  color: #333;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Dropdown navigation styles */
.group:hover .group-hover\:opacity-100 {
  opacity: 1 !important;
}

.group:hover .group-hover\:visible {
  visibility: visible !important;
}

/* Ensure dropdown stays open when hovering over dropdown items */
.dropdown-menu {
  pointer-events: none;
}

.group:hover .dropdown-menu {
  pointer-events: auto;
}

/* Add a small delay to prevent flickering */
.dropdown-transition {
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  /* Ensure touch targets are at least 44px */
  button, a {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on mobile */
  body {
    font-size: 16px;
    line-height: 1.5;
  }

  /* Better spacing for mobile forms */
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px;
  }

  /* Improve button spacing on mobile */
  .mobile-button-spacing > * + * {
    margin-top: 12px;
  }

  /* Better card spacing on mobile */
  .mobile-card-spacing {
    margin-bottom: 16px;
  }

  /* Ensure images are responsive */
  img {
    max-width: 100%;
    height: auto;
  }
}

/* Tablet-specific improvements */
@media (min-width: 769px) and (max-width: 1024px) {
  /* Adjust grid layouts for tablets */
  .tablet-grid-adjust {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Improve focus states for accessibility */
button:focus, a:focus, input:focus, textarea:focus, select:focus {
  outline: 2px solid #f5a623;
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Touch manipulation for better mobile performance */
.touch-manipulation {
  touch-action: manipulation;
}

/* Prevent text selection on mobile menu items */
.mobile-nav-item {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* Ensure mobile menu has proper z-index */
.mobile-menu {
  z-index: 9999;
}

/* Better mobile button styling */
@media (max-width: 768px) {
  button, a[role="button"] {
    -webkit-tap-highlight-color: rgba(245, 166, 35, 0.3);
    tap-highlight-color: rgba(245, 166, 35, 0.3);
  }

  /* Improve mobile navigation */
  .mobile-nav-container {
    position: relative;
    z-index: 1000;
  }

  /* Ensure proper touch targets */
  .mobile-touch-target {
    min-height: 48px;
    min-width: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
