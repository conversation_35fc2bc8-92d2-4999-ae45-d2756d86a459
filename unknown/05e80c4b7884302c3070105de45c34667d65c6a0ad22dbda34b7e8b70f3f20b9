import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export default function Footer() {
  return (
    <footer className="bg-black text-white py-12">
      <div className="max-w-7xl mx-auto px-4 grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Column 1 */}
        <div>
          <h2 className="text-2xl font-bold mb-4">MK Academia & Lab</h2>
          <p className="mb-6 text-gray-300">
            The MK-academia offers a wide range of high-quality research services by our panel of experts and using
            advanced analytical methods.
          </p>
          <div className="flex space-x-4 mb-6">
            <a href="#" className="text-white hover:text-gray-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="w-6 h-6"
              >
                <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
              </svg>
            </a>
            <a href="#" className="text-white hover:text-gray-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="w-6 h-6"
              >
                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                <rect x="2" y="9" width="4" height="12"></rect>
                <circle cx="4" cy="4" r="2"></circle>
              </svg>
            </a>
            <a href="#" className="text-white hover:text-gray-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="w-6 h-6"
              >
                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
              </svg>
            </a>
            <a href="#" className="text-white hover:text-gray-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="currentColor"
                className="w-6 h-6"
              >
                <path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm3 8a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm-6 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0zm4.5 7a8 8 0 0 1-5-1.5 1 1 0 0 1 1-1.5 6 6 0 0 0 4 0 1 1 0 0 1 1 1.5 8 8 0 0 1-5 1.5z" />
              </svg>
            </a>
          </div>
          <div className="flex space-x-4">
            <Link href="/privacy" className="flex items-center text-white hover:text-gray-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-5 h-5 mr-2"
              >
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
              </svg>
              Privacy Policy
            </Link>
            <Link href="/terms-of-use" className="flex items-center text-white hover:text-gray-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-5 h-5 mr-2"
              >
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                <polyline points="14 2 14 8 20 8" />
                <line x1="16" y1="13" x2="8" y2="13" />
                <line x1="16" y1="17" x2="8" y2="17" />
                <polyline points="10 9 9 9 8 9" />
              </svg>
              Terms of Use
            </Link>
          </div>
        </div>

        {/* Column 2 */}
        <div>
          <h2 className="text-2xl font-bold mb-4">Our Services</h2>
          <ul className="space-y-2">
            <li>
              <Link href="/services" className="text-gray-300 hover:text-white">
                Statistical Analysis
              </Link>
            </li>
            <li>
              <Link href="/services" className="text-gray-300 hover:text-white">
                Academic Writing
              </Link>
            </li>
            <li>
              <Link href="/services" className="text-gray-300 hover:text-white">
                Grant Writing
              </Link>
            </li>
            <li>
              <Link href="/services" className="text-gray-300 hover:text-white">
                Survey Administration
              </Link>
            </li>
            <li>
              <Link href="/services" className="text-gray-300 hover:text-white">
                Article Writing
              </Link>
            </li>
          </ul>
        </div>

        {/* Column 3 */}
        <div>
          <h2 className="text-2xl font-bold mb-4">News Letter</h2>
          <form className="space-y-4">
            <div>
              <input
                type="text"
                placeholder="Name*"
                className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white"
              />
            </div>
            <div>
              <input
                type="email"
                placeholder="Email*"
                className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white"
              />
            </div>
            <div className="bg-white p-3 rounded">
              <div className="text-black text-sm">I'm not a robot</div>
            </div>
            <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white px-8 py-2 rounded">Submit</Button>
          </form>
        </div>
      </div>
    </footer>
  )
}
