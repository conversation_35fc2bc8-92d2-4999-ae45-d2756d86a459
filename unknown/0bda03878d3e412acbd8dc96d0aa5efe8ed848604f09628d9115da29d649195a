import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"

export default function QuoteRequestPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Page Content */}
      <div className="flex-1 py-8 sm:py-16 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Back Navigation */}
          <div className="mb-6">
            <Link href="/services" className="inline-flex items-center text-[#0a2a5e] hover:text-[#f5a623] transition-colors">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Services
            </Link>
          </div>

          <h1 className="text-3xl sm:text-4xl font-bold text-center text-[#0a2a5e] mb-8 sm:mb-16">
            Research Lab Services Quote Request
          </h1>

          <div className="bg-white rounded-lg shadow-md border border-gray-100 p-6 sm:p-8">
            <h2 className="text-2xl sm:text-3xl font-bold text-center text-[#0a2a5e] mb-8 sm:mb-12">
              Request Quote
            </h2>

            <form className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Your Name *
                  </Label>
                  <Input 
                    id="name"
                    type="text" 
                    placeholder="Enter your full name" 
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#f5a623] focus:border-transparent" 
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Your Email *
                  </Label>
                  <Input 
                    id="email"
                    type="email" 
                    placeholder="Enter your email address" 
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#f5a623] focus:border-transparent" 
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Number (Optional)
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="Enter your phone number"
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#f5a623] focus:border-transparent"
                  />
                </div>

                <div>
                  <Label htmlFor="lab-test" className="block text-sm font-medium text-gray-700 mb-2">
                    Research Lab Test *
                  </Label>
                  <Select required>
                    <SelectTrigger className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#f5a623] focus:border-transparent">
                      <SelectValue placeholder="Select research lab test" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="histopathology">Histopathology</SelectItem>
                      <SelectItem value="molecular">Molecular Testing</SelectItem>
                      <SelectItem value="immunology">Immunology</SelectItem>
                      <SelectItem value="microbiology">Microbiology</SelectItem>
                      <SelectItem value="biochemistry">Biochemistry</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="mt-8">
                <h3 className="text-lg font-semibold text-[#0a2a5e] mb-4">
                  Histopathology Research Services
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Select the services you require (check all that apply):
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-start space-x-3">
                    <Checkbox id="processing-staining" className="mt-1" />
                    <Label htmlFor="processing-staining" className="font-normal text-sm cursor-pointer">
                      Processing To Staining
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox id="processing-block" className="mt-1" />
                    <Label htmlFor="processing-block" className="font-normal text-sm cursor-pointer">
                      Processing To Block
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox id="sectioning" className="mt-1" />
                    <Label htmlFor="sectioning" className="font-normal text-sm cursor-pointer">
                      Sectioning
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox id="he-staining" className="mt-1" />
                    <Label htmlFor="he-staining" className="font-normal text-sm cursor-pointer">
                      H&E Staining
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox id="ihc-staining" className="mt-1" />
                    <Label htmlFor="ihc-staining" className="font-normal text-sm cursor-pointer">
                      IHC Staining
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox id="scanning" className="mt-1" />
                    <Label htmlFor="scanning" className="font-normal text-sm cursor-pointer">
                      Scanning
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox id="scoring-conventional" className="mt-1" />
                    <Label htmlFor="scoring-conventional" className="font-normal text-sm cursor-pointer">
                      Scoring for Conventional Slides
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox id="scoring-tma" className="mt-1" />
                    <Label htmlFor="scoring-tma" className="font-normal text-sm cursor-pointer">
                      Scoring TMA Slides
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox id="detailed-reporting" className="mt-1" />
                    <Label htmlFor="detailed-reporting" className="font-normal text-sm cursor-pointer">
                      Detailed Reporting
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox id="data-analysis" className="mt-1" />
                    <Label htmlFor="data-analysis" className="font-normal text-sm cursor-pointer">
                      Data Analysis
                    </Label>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Description *
                </Label>
                <Textarea
                  id="description"
                  placeholder="Briefly describe the required services, timeline, and any specific requirements"
                  className="w-full p-3 border border-gray-300 rounded-md min-h-[120px] focus:ring-2 focus:ring-[#f5a623] focus:border-transparent"
                  required
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button 
                  type="submit" 
                  className="flex-1 bg-[#f5a623] hover:bg-[#e69816] text-white py-3 rounded-md font-medium transition-colors"
                >
                  Submit Request
                </Button>
                <Link href="/services" className="flex-1">
                  <Button 
                    type="button" 
                    variant="outline"
                    className="w-full border-[#0a2a5e] text-[#0a2a5e] hover:bg-[#0a2a5e] hover:text-white py-3 rounded-md font-medium transition-colors"
                  >
                    Back to Services
                  </Button>
                </Link>
              </div>
            </form>
          </div>

          {/* Contact Information */}
          <div className="mt-12 bg-gray-50 rounded-lg p-6 text-center">
            <h3 className="text-lg font-semibold text-[#0a2a5e] mb-4">
              Need Help with Your Request?
            </h3>
            <p className="text-gray-700 mb-4">
              Our team is here to assist you with any questions about our services.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/contact">
                <Button variant="outline" className="border-[#0a2a5e] text-[#0a2a5e] hover:bg-[#0a2a5e] hover:text-white">
                  Contact Us
                </Button>
              </Link>
              <a href="mailto:<EMAIL>" className="inline-block">
                <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white">
                  Email Us Directly
                </Button>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
