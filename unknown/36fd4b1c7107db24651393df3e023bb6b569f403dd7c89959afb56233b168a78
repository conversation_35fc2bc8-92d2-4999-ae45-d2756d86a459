# Next.js Static Export .htaccess Configuration
# This file handles routing, MIME types, and server configuration for shared hosting

# Enable URL rewriting
RewriteEngine On

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# MIME Types for Next.js assets
AddType application/javascript .js
AddType application/json .json
AddType text/css .css
AddType image/svg+xml .svg
AddType image/webp .webp
AddType font/woff2 .woff2
AddType font/woff .woff
AddType font/ttf .ttf
AddType font/eot .eot

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache Control for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
</IfModule>

# Handle Next.js static export routing
# Redirect root to index.html if it exists
DirectoryIndex index.html

# Handle clean URLs for Next.js pages
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} ^/([^/]+)/?$
RewriteCond %{DOCUMENT_ROOT}/%1.html -f
RewriteRule ^([^/]+)/?$ /$1.html [L]

# Handle nested routes (like /services/medical-research-support)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} ^/([^/]+)/([^/]+)/?$
RewriteCond %{DOCUMENT_ROOT}/%1/%2.html -f
RewriteRule ^([^/]+)/([^/]+)/?$ /$1/$2.html [L]

# Handle deeper nested routes if needed
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} ^/([^/]+)/([^/]+)/([^/]+)/?$
RewriteCond %{DOCUMENT_ROOT}/%1/%2/%3.html -f
RewriteRule ^([^/]+)/([^/]+)/([^/]+)/?$ /$1/$2/$3.html [L]

# Fallback to index.html for client-side routing (SPA behavior)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/_next/
RewriteCond %{REQUEST_URI} !^/api/
RewriteCond %{REQUEST_URI} !\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot|json)$
RewriteRule . /index.html [L]

# Ensure _next directory is accessible
RewriteCond %{REQUEST_URI} ^/_next/
RewriteRule ^(.*)$ /$1 [L]

# Handle 404 errors
ErrorDocument 404 /404.html

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to source files
<FilesMatch "\.(tsx?|jsx?|ts|js)$">
    <RequireAll>
        Require all denied
    </RequireAll>
</FilesMatch>

# Allow access to built JS files in _next directory
<Directory "_next">
    <FilesMatch "\.js$">
        Require all granted
    </FilesMatch>
</Directory>
