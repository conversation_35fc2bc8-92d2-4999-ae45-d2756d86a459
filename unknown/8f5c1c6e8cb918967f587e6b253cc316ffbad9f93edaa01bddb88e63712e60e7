import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, CheckCircle, Users, BarChart3, Brain, Microscope, FileText } from "lucide-react"

export default function ServicesPage() {
  const services = [
    {
      title: "Medical Research Support",
      description: "Comprehensive support for medical research projects, from study design to data analysis and publication.",
      image: "/2.jpg",
      href: "/services/medical-research-support",
      icon: <Microscope className="h-8 w-8 text-[#f5a623]" />,
      features: ["Clinical Research", "Laboratory Research", "Study Design", "Data Analysis"]
    },
    {
      title: "Non-Medical Research Support",
      description: "Expert support for research across various non-medical disciplines including social sciences and business.",
      image: "/Research-Support6.jpg",
      href: "/services/non-medical-research-support",
      icon: <Users className="h-8 w-8 text-[#f5a623]" />,
      features: ["Social Sciences", "Business Research", "Environmental Studies", "Technology Research"]
    },
    {
      title: "Brain Tumor Consultation",
      description: "Specialized consultation services for brain tumor diagnosis, treatment planning, and research collaboration.",
      image: "/39.jpg",
      href: "/services/brain-tumor-consultation",
      icon: <Brain className="h-8 w-8 text-[#f5a623]" />,
      features: ["Diagnostic Analysis", "Treatment Planning", "Molecular Profiling", "Research Support"]
    },
    {
      title: "Statistical Consultancy",
      description: "Expert statistical analysis and consultation for research projects, clinical trials, and data-driven decisions.",
      image: "/placeholder.jpg",
      href: "/services/statistical-consultancy",
      icon: <BarChart3 className="h-8 w-8 text-[#f5a623]" />,
      features: ["Clinical Trial Statistics", "Biostatistics", "Data Analysis", "Statistical Modeling"]
    },
    {
      title: "Survey Administration",
      description: "Comprehensive survey design, implementation, and administration services for reliable data collection.",
      image: "/Online-Survey.jpg",
      href: "/services/survey-administration",
      icon: <FileText className="h-8 w-8 text-[#f5a623]" />,
      features: ["Survey Design", "Data Collection", "Response Analysis", "Reporting"]
    }
  ]

  return (
    <div className="min-h-screen flex flex-col">
      {/* Hero Section */}
      <section
        className="relative bg-gradient-to-br from-[#0a2a5e] via-[#1e3a8a] to-[#3b82f6] text-white py-20"
        style={{
          backgroundImage: `linear-gradient(rgba(10, 42, 94, 0.8), rgba(59, 130, 246, 0.8)), url('/Banner.jpg')`,
        }}
      >
        <div className="max-w-6xl mx-auto px-4 text-center">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6">
            Our Research & Lab Services
          </h1>
          <p className="text-lg sm:text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            MK Academia offers a comprehensive range of high-quality educational, research, and lab management services
            delivered by our panel of expert professionals across multiple disciplines.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/services/quote-request">
              <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white px-8 py-3">
                Get Started Today
              </Button>
            </Link>
            <Link href="/contact">
              <Button
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-[#0a2a5e] px-8 py-3 bg-transparent"
              >
                Request Consultation
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#0a2a5e] mb-4">
              Explore Our Services
            </h2>
            <p className="text-gray-700 max-w-2xl mx-auto">
              Choose from our comprehensive range of research and laboratory services,
              each designed to meet the highest standards of quality and precision.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md border border-gray-100 overflow-hidden hover:shadow-lg transition-shadow duration-300 mobile-card-spacing">
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={service.image}
                    alt={service.title}
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-3">
                    {service.icon}
                    <h3 className="text-xl font-semibold text-[#0a2a5e] ml-3">
                      {service.title}
                    </h3>
                  </div>
                  <p className="text-gray-700 mb-4 text-sm sm:text-base">
                    {service.description}
                  </p>
                  <div className="mb-4">
                    <h4 className="font-medium text-[#0a2a5e] mb-2">Key Features:</h4>
                    <ul className="space-y-1">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <Link href={service.href}>
                    <Button className="w-full bg-[#f5a623] hover:bg-[#e69816] text-white flex items-center justify-center">
                      Learn More
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#0a2a5e] mb-4">
              Why Choose MK Academia?
            </h2>
            <p className="text-gray-700 max-w-2xl mx-auto">
              We combine expertise, innovation, and dedication to deliver exceptional research and laboratory services.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-[#f5a623] rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-[#0a2a5e] mb-3">Expert Team</h3>
              <p className="text-gray-700">
                Our panel of experienced professionals brings deep expertise across multiple research disciplines.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#f5a623] rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-[#0a2a5e] mb-3">Quality Assurance</h3>
              <p className="text-gray-700">
                We maintain the highest standards of quality and precision in all our research and laboratory services.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#f5a623] rounded-full flex items-center justify-center mx-auto mb-4">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-[#0a2a5e] mb-3">Data-Driven Results</h3>
              <p className="text-gray-700">
                Our evidence-based approach ensures reliable, actionable insights for your research projects.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Lab Services Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#0a2a5e] mb-4">
              Research Lab Services
            </h2>
            <p className="text-gray-700 max-w-2xl mx-auto mb-6">
              Advanced laboratory testing and analysis services to support your research objectives.
            </p>
            <Link href="/services/quote-request">
              <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white px-6 py-2">
                Request Lab Services Quote
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
              <div className="w-12 h-12 bg-[#0a2a5e] rounded-full flex items-center justify-center mx-auto mb-4">
                <Microscope className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-semibold text-[#0a2a5e] mb-2">Histopathology</h3>
              <p className="text-sm text-gray-600">
                Comprehensive tissue analysis and diagnostic services
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
              <div className="w-12 h-12 bg-[#0a2a5e] rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-semibold text-[#0a2a5e] mb-2">Molecular Testing</h3>
              <p className="text-sm text-gray-600">
                Advanced molecular diagnostics and genetic analysis
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
              <div className="w-12 h-12 bg-[#0a2a5e] rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                </svg>
              </div>
              <h3 className="font-semibold text-[#0a2a5e] mb-2">Immunology</h3>
              <p className="text-sm text-gray-600">
                Immune system analysis and antibody testing
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
              <div className="w-12 h-12 bg-[#0a2a5e] rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
              </div>
              <h3 className="font-semibold text-[#0a2a5e] mb-2">Biochemistry</h3>
              <p className="text-sm text-gray-600">
                Chemical analysis and metabolic studies
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-[#0a2a5e] text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-2xl sm:text-3xl font-bold mb-6">Ready to Get Started?</h2>
          <p className="text-lg sm:text-xl mb-8 text-blue-100">
            Contact us today to discuss your research needs and discover how our expert team can support your project.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4 mobile-button-spacing">
            <Link href="/contact">
              <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white px-8 py-3">
                Get Free Consultation
              </Button>
            </Link>
            <Link href="/registration">
              <Button
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-[#0a2a5e] px-8 py-3 bg-transparent"
              >
                Register Now
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
