import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"

export default function StatisticalConsultancyPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Hero Section */}
      <section
        className="relative bg-gradient-to-br from-[#0a2a5e] via-[#1e3a8a] to-[#3b82f6] text-white py-20"
        style={{
          backgroundImage: `linear-gradient(rgba(10, 42, 94, 0.8), rgba(59, 130, 246, 0.8)), url('/placeholder.svg?height=600&width=1200')`,
        }}
      >
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-1/2">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">Statistical Consultancy Services</h1>
              <p className="text-xl mb-8 text-blue-100">
                Expert statistical analysis and consultation for research projects, clinical trials, and data-driven
                decision making across all disciplines.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white px-8 py-3">Get Consultation</Button>
                <Button
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-[#0a2a5e] px-8 py-3 bg-transparent"
                >
                  View Portfolio
                </Button>
              </div>
            </div>
            <div className="lg:w-1/2">
              <div className="relative">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center justify-center">
                    <BarChart3 className="w-16 h-16 text-blue-200" />
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center justify-center">
                    <TrendingUp className="w-16 h-16 text-green-300" />
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center justify-center">
                    <PieChart className="w-16 h-16 text-yellow-300" />
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 flex items-center justify-center">
                    <BarChart3 className="w-16 h-16 text-purple-300" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#0a2a5e] mb-4">Our Statistical Consultancy Services</h2>
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              We provide comprehensive statistical support for researchers, clinicians, and organizations across various
              fields, ensuring robust and reliable data analysis.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
              <div className="w-12 h-12 bg-[#0a2a5e] rounded-lg flex items-center justify-center mb-4">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-[#0a2a5e] mb-3">Descriptive Statistics</h3>
              <p className="text-gray-700 mb-4">
                Comprehensive data summarization, visualization, and exploratory data analysis to understand your
                dataset.
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Data cleaning and preparation</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Summary statistics calculation</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Data visualization and charts</span>
                </li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
              <div className="w-12 h-12 bg-[#0a2a5e] rounded-lg flex items-center justify-center mb-4">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-[#0a2a5e] mb-3">Inferential Statistics</h3>
              <p className="text-gray-700 mb-4">
                Advanced statistical testing and analysis to draw meaningful conclusions from your data.
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Hypothesis testing</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Confidence intervals</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">P-value interpretation</span>
                </li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
              <div className="w-12 h-12 bg-[#0a2a5e] rounded-lg flex items-center justify-center mb-4">
                <PieChart className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-[#0a2a5e] mb-3">Multivariate Analysis</h3>
              <p className="text-gray-700 mb-4">
                Complex statistical modeling and analysis for multiple variables and advanced research questions.
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Regression analysis</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Factor analysis</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                  <span className="text-sm text-gray-600">Cluster analysis</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Specialized Services */}
      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-[#0a2a5e] mb-12 text-center">Specialized Statistical Services</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-2xl font-semibold text-[#0a2a5e] mb-4">Clinical Trial Statistics</h3>
              <p className="text-gray-700 mb-6">
                Specialized statistical support for clinical research, including study design, sample size calculation,
                and regulatory compliance.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Sample size and power calculations</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Randomization and blinding strategies</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Interim analysis and safety monitoring</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Regulatory submission support</span>
                </li>
              </ul>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-md">
              <h3 className="text-2xl font-semibold text-[#0a2a5e] mb-4">Biostatistics & Epidemiology</h3>
              <p className="text-gray-700 mb-6">
                Advanced biostatistical methods for medical research, public health studies, and epidemiological
                investigations.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Survival analysis and time-to-event data</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Longitudinal data analysis</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Meta-analysis and systematic reviews</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-[#f5a623] mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-gray-700">Diagnostic test evaluation</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-[#0a2a5e] mb-12 text-center">Our Consultation Process</h2>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                1
              </div>
              <h3 className="text-lg font-semibold text-[#0a2a5e] mb-2">Initial Consultation</h3>
              <p className="text-gray-700 text-sm">
                We discuss your research objectives, data structure, and analytical requirements.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                2
              </div>
              <h3 className="text-lg font-semibold text-[#0a2a5e] mb-2">Analysis Plan</h3>
              <p className="text-gray-700 text-sm">
                We develop a comprehensive statistical analysis plan tailored to your specific needs.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                3
              </div>
              <h3 className="text-lg font-semibold text-[#0a2a5e] mb-2">Data Analysis</h3>
              <p className="text-gray-700 text-sm">
                We perform the statistical analysis using appropriate methods and software.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                4
              </div>
              <h3 className="text-lg font-semibold text-[#0a2a5e] mb-2">Results & Report</h3>
              <p className="text-gray-700 text-sm">
                We provide detailed results interpretation and comprehensive statistical reports.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-[#0a2a5e] text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Get Started?</h2>
          <p className="text-xl mb-8 text-blue-100">
            Contact us today to discuss your statistical analysis needs and get expert consultation for your research
            project.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/contact">
              <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white px-8 py-3">Get Free Consultation</Button>
            </Link>
            <Link href="/services">
              <Button
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-[#0a2a5e] px-8 py-3 bg-transparent"
              >
                View All Services
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
