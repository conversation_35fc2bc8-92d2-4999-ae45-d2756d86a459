import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle } from "lucide-react"

export default function NonMedicalResearchSupportPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Page Content */}
      <div className="flex-1 py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center text-[#0a2a5e] mb-8">Non-Medical Research Support</h1>

          <div className="mb-12 text-center">
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              MK Academia provides comprehensive support for non-medical research projects across various disciplines.
              Our team of experienced researchers offers expertise in study design, data collection, analysis, and
              publication to help you achieve your research goals.
            </p>
          </div>

          <div className="mb-16">
            <h2 className="text-2xl font-semibold text-[#0a2a5e] mb-6 text-center">
              Our Non-Medical Research Services
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-4">Social Sciences Research</h3>
                <p className="text-gray-700 mb-4">
                  We support research in psychology, sociology, education, and other social sciences disciplines. Our
                  services include survey design, qualitative research methods, statistical analysis, and more.
                </p>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Survey and questionnaire design</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Qualitative data collection and analysis</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Statistical analysis of social data</span>
                  </li>
                </ul>
                <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white w-full">Learn More</Button>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-4">Business & Management Research</h3>
                <p className="text-gray-700 mb-4">
                  We provide support for research in business, management, economics, and related fields. Our services
                  include market research, organizational studies, financial analysis, and more.
                </p>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Market research and analysis</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Organizational behavior studies</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Economic and financial data analysis</span>
                  </li>
                </ul>
                <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white w-full">Learn More</Button>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-4">Environmental Research</h3>
                <p className="text-gray-700 mb-4">
                  We support research in environmental science, ecology, conservation, and sustainability. Our services
                  include field study design, environmental data analysis, and impact assessments.
                </p>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Environmental impact assessments</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Ecological data collection and analysis</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Sustainability research</span>
                  </li>
                </ul>
                <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white w-full">Learn More</Button>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-4">Technology & Engineering Research</h3>
                <p className="text-gray-700 mb-4">
                  We provide support for research in technology, engineering, computer science, and related fields. Our
                  services include prototype testing, algorithm development, and technical documentation.
                </p>
                <ul className="space-y-2 mb-6">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Technical research design</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Data analysis and visualization</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Technical documentation and reporting</span>
                  </li>
                </ul>
                <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white w-full">Learn More</Button>
              </div>
            </div>
          </div>

          <div className="mb-16">
            <h2 className="text-2xl font-semibold text-[#0a2a5e] mb-6 text-center">Our Research Support Process</h2>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
                <div className="w-12 h-12 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  1
                </div>
                <h3 className="text-lg font-medium text-[#0a2a5e] mb-2">Initial Consultation</h3>
                <p className="text-gray-700 text-sm">
                  We discuss your research needs and objectives to understand your project requirements.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
                <div className="w-12 h-12 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  2
                </div>
                <h3 className="text-lg font-medium text-[#0a2a5e] mb-2">Research Design</h3>
                <p className="text-gray-700 text-sm">
                  We develop a comprehensive research plan tailored to your specific objectives.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
                <div className="w-12 h-12 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  3
                </div>
                <h3 className="text-lg font-medium text-[#0a2a5e] mb-2">Data Collection & Analysis</h3>
                <p className="text-gray-700 text-sm">
                  We collect and analyze data using appropriate methodologies and tools.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
                <div className="w-12 h-12 bg-[#0a2a5e] text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  4
                </div>
                <h3 className="text-lg font-medium text-[#0a2a5e] mb-2">Results & Reporting</h3>
                <p className="text-gray-700 text-sm">
                  We provide comprehensive reports and support for publication or implementation.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-8 rounded-lg">
            <h2 className="text-2xl font-semibold text-[#0a2a5e] mb-6 text-center">
              Why Choose Our Non-Medical Research Support?
            </h2>

            <div className="flex flex-col md:flex-row gap-8 items-center">
              <div className="md:w-1/2">
<Image
  src="/Research-Support6.jpg"
  alt="Research Team"
  width={400}
  height={300}
  className="rounded-lg shadow-md w-full h-auto"
/>
              </div>
              <div className="md:w-1/2">
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Multidisciplinary team of research experts</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Customized research solutions for your specific needs</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Rigorous methodological approach to ensure quality</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Commitment to research integrity and ethics</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Proven track record of successful research projects</span>
                  </li>
                </ul>

                <div className="mt-6">
                  <Link href="/contact">
                    <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white">
                      Contact Us for Research Support
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
