import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function About() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-[#0a2a5e] text-white py-16">
        <div className="max-w-4xl mx-auto text-center px-4">
          <h1 className="text-4xl font-bold mb-6">About MK Academia</h1>
          <p className="text-lg mb-8">Providing high-quality educational, research and lab management services</p>
        </div>
      </section>

      {/* About Content */}
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row gap-8 mb-12">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold text-[#0a2a5e] mb-6">Our Mission</h2>
              <p className="text-gray-700 mb-4">
                MK Academia is dedicated to providing high-quality educational, research, and lab management services to
                academic institutions, researchers, and healthcare professionals. Our mission is to support the
                advancement of knowledge through rigorous scientific investigation and educational excellence.
              </p>
              <p className="text-gray-700 mb-4">
                We strive to be a trusted partner in the research journey, offering expertise and support at every stage
                of the process, from study design to publication.
              </p>
            </div>
            <div className="md:w-1/2">
<Image
  src="/2.jpg"
  alt="About MK Academia"
  width={500}
  height={300}
  className="rounded-lg shadow-md w-full h-auto"
/>
            </div>
          </div>

          <div className="mb-12">
            <h2 className="text-3xl font-bold text-[#0a2a5e] mb-6">Our Team</h2>
            <p className="text-gray-700 mb-6">
              Our team consists of highly qualified professionals with expertise in various fields of research,
              education, and healthcare. We bring together a diverse range of skills and experiences to provide
              comprehensive support for your research and educational needs.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
                <div className="w-24 h-24 rounded-full bg-gray-200 mx-auto mb-4"></div>
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-2">Dr. John Smith</h3>
                <p className="text-gray-600 mb-2">Research Director</p>
                <p className="text-gray-700 text-sm">
                  PhD in Medical Sciences with over 15 years of experience in clinical research and education.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
                <div className="w-24 h-24 rounded-full bg-gray-200 mx-auto mb-4"></div>
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-2">Dr. Sarah Johnson</h3>
                <p className="text-gray-600 mb-2">Lab Management Specialist</p>
                <p className="text-gray-700 text-sm">
                  PhD in Molecular Biology with expertise in laboratory operations and management.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 text-center">
                <div className="w-24 h-24 rounded-full bg-gray-200 mx-auto mb-4"></div>
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-2">Prof. Michael Lee</h3>
                <p className="text-gray-600 mb-2">Educational Consultant</p>
                <p className="text-gray-700 text-sm">
                  Professor of Education with extensive experience in curriculum development and academic writing.
                </p>
              </div>
            </div>
          </div>

          <div className="mb-12">
            <h2 className="text-3xl font-bold text-[#0a2a5e] mb-6">Our Approach</h2>
            <p className="text-gray-700 mb-6">
              At MK Academia, we believe in a collaborative approach to research and education. We work closely with our
              clients to understand their specific needs and goals, and develop customized solutions to help them
              achieve success.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-3">Client-Centered</h3>
                <p className="text-gray-700">
                  We prioritize our clients' needs and goals, tailoring our services to meet their specific requirements
                  and expectations.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-3">Evidence-Based</h3>
                <p className="text-gray-700">
                  Our recommendations and practices are grounded in the latest research and best practices in the field.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-3">Innovative</h3>
                <p className="text-gray-700">
                  We continuously explore new approaches and technologies to enhance the quality and efficiency of our
                  services.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-3">Ethical</h3>
                <p className="text-gray-700">
                  We adhere to the highest ethical standards in all our work, ensuring integrity, confidentiality, and
                  respect for all stakeholders.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-8 rounded-lg text-center">
            <h2 className="text-3xl font-bold text-[#0a2a5e] mb-6">Ready to Work With Us?</h2>
            <p className="text-gray-700 mb-6 max-w-2xl mx-auto">
              Contact us today to learn more about our services and how we can support your research and educational
              needs.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/services">
                <Button
                  variant="outline"
                  className="border-[#0a2a5e] text-[#0a2a5e] hover:bg-[#0a2a5e] hover:text-white"
                >
                  Explore Our Services
                </Button>
              </Link>
              <Link href="/contact">
                <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white">Contact Us</Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
