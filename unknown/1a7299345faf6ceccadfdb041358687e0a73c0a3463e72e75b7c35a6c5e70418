import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle } from "lucide-react"

export default function MedicalResearchSupportPage() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Page Content */}
      <div className="flex-1 py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center text-[#0a2a5e] mb-8">Medical Research Support</h1>

          <div className="mb-12 text-center">
            <p className="text-lg text-gray-700 max-w-3xl mx-auto">
              MK Academia provides comprehensive support for medical research projects, from study design to data
              analysis and publication. Our team of experienced researchers and clinicians is dedicated to advancing
              medical knowledge through rigorous scientific investigation.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 flex flex-col">
              <h2 className="text-2xl font-semibold text-[#0a2a5e] mb-4">Clinical Research</h2>
              <p className="text-gray-700 mb-4 flex-grow">
                We support clinical research across various medical specialties, helping researchers design studies,
                collect and analyze data, and interpret results. Our team has expertise in clinical trials,
                observational studies, and retrospective analyses.
              </p>
              <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white w-full mt-auto">Learn More</Button>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 flex flex-col">
              <h2 className="text-2xl font-semibold text-[#0a2a5e] mb-4">Laboratory Research</h2>
              <p className="text-gray-700 mb-4 flex-grow">
                Our laboratory research support services include experimental design, protocol development, sample
                processing, and data analysis. We have expertise in various laboratory techniques, including
                histopathology, molecular biology, and cell culture.
              </p>
              <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white w-full mt-auto">Learn More</Button>
            </div>
          </div>

          <div className="mb-16">
            <h2 className="text-2xl font-semibold text-[#0a2a5e] mb-6 text-center">
              Our Medical Research Support Services
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-3">Study Design</h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Research question formulation</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Study protocol development</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Sample size calculation</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Ethical considerations</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-3">Data Collection</h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Case report form design</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Database development</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Data quality control</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Sample collection and processing</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100">
                <h3 className="text-xl font-medium text-[#0a2a5e] mb-3">Analysis & Publication</h3>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Statistical analysis</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Results interpretation</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Manuscript preparation</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Journal submission support</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-8 rounded-lg">
            <h2 className="text-2xl font-semibold text-[#0a2a5e] mb-6 text-center">
              Why Choose Our Medical Research Support?
            </h2>

            <div className="flex flex-col md:flex-row gap-8 items-center">
              <div className="md:w-1/2">
<Image
  src="/2.jpg"
  alt="Medical Research Team"
  width={400}
  height={300}
  className="rounded-lg shadow-md w-full h-auto"
/>
              </div>
              <div className="md:w-1/2">
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Experienced team of researchers and clinicians</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Expertise across various medical specialties</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Rigorous methodological approach</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Commitment to research integrity and ethics</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-[#f5a623] mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">Track record of successful publications</span>
                  </li>
                </ul>

                <div className="mt-6">
                  <Link href="/contact">
                    <Button className="bg-[#f5a623] hover:bg-[#e69816] text-white">
                      Contact Us for Research Support
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
