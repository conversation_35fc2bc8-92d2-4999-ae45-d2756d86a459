import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function Registration() {
  return (
    <div className="min-h-screen p-8">
      <div className="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-6 text-center text-primary">Registration Form</h1>

        <form className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Full Name</Label>
            <Input id="name" placeholder="Enter your full name" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input id="email" type="email" placeholder="Enter your email" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input id="phone" placeholder="Enter your phone number" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="institution">Institution/Organization</Label>
            <Input id="institution" placeholder="Enter your institution or organization" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="service">Service Interested In</Label>
            <select id="service" className="w-full p-2 border rounded-md">
              <option value="">Select a service</option>
              <option value="medical">Medical Research Support</option>
              <option value="non-medical">Non-Medical Research Support</option>
              <option value="brain-tumor">Brain Tumor Consultation</option>
              <option value="lab">Lab Management</option>
            </select>
          </div>

          <Button type="submit" className="w-full bg-secondary hover:bg-secondary/90">
            Register
          </Button>
        </form>
      </div>
    </div>
  )
}
