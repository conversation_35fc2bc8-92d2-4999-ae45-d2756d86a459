# 🚀 Shared Hosting Deployment Guide for Next.js Static Export

## 📋 Pre-Deployment Checklist

### 1. Build Your Application
```bash
npm run build
```
This creates the `out` directory with all static files.

### 2. Verify Build Output
Run the deployment check script:
```bash
node deployment-check.js
```

## 🔧 Deployment Steps

### Step 1: Prepare Files for Upload
1. **Navigate to the `out` directory** - This contains all files needed for deployment
2. **Verify .htaccess file exists** in the `out` directory
3. **Check file structure**:
   ```
   out/
   ├── .htaccess          ← Critical for routing
   ├── index.html         ← Homepage
   ├── about.html         ← About page
   ├── contact.html       ← Contact page
   ├── registration.html  ← Registration page
   ├── services.html      ← Services page
   ├── 404.html          ← Error page
   ├── services/         ← Service subpages
   ├── _next/            ← Next.js assets
   └── *.jpg, *.png      ← Images
   ```

### Step 2: Upload to Shared Hosting
1. **Access your hosting control panel** (cPanel, Plesk, etc.)
2. **Navigate to File Manager** or use FTP client
3. **Go to your domain's root directory** (usually `public_html` or `www`)
4. **Upload ALL contents** from the `out` directory to the root
5. **Ensure .htaccess file is uploaded** and visible

### Step 3: Set File Permissions
Set the following permissions:
- **Files**: 644 (readable by all, writable by owner)
- **Directories**: 755 (executable/searchable by all)
- **.htaccess**: 644

### Step 4: Verify Server Requirements
Your shared hosting must support:
- ✅ Apache web server
- ✅ mod_rewrite module (for URL rewriting)
- ✅ mod_headers module (for security headers)
- ✅ mod_deflate module (for compression)

## 🐛 Common Issues & Solutions

### Issue 1: Pages Show 404 Errors
**Symptoms**: Direct links to `/about`, `/contact`, etc. return 404
**Solution**: 
- Verify .htaccess file is uploaded and working
- Check if mod_rewrite is enabled on your hosting
- Contact hosting support to enable mod_rewrite

### Issue 2: CSS/JS Files Not Loading
**Symptoms**: Website appears unstyled or broken
**Solutions**:
- Check if `_next` directory was uploaded completely
- Verify MIME types in .htaccess
- Check browser console for 404 errors on assets

### Issue 3: Images Not Displaying
**Symptoms**: Broken image icons or missing images
**Solutions**:
- Verify all image files were uploaded
- Check file name case sensitivity (Linux servers are case-sensitive)
- Ensure image paths in HTML match actual file names

### Issue 4: Routing Not Working
**Symptoms**: Navigation between pages doesn't work
**Solutions**:
- Confirm .htaccess rules are correct
- Test if Apache mod_rewrite is working
- Check for conflicting .htaccess rules

## 🔍 Testing Your Deployment

### 1. Test All Routes
Visit these URLs directly in your browser:
- `https://yourdomain.com/` (Homepage)
- `https://yourdomain.com/about` (About page)
- `https://yourdomain.com/contact` (Contact page)
- `https://yourdomain.com/services` (Services page)
- `https://yourdomain.com/registration` (Registration page)
- `https://yourdomain.com/services/medical-research-support`

### 2. Check Browser Console
1. Open browser developer tools (F12)
2. Check Console tab for JavaScript errors
3. Check Network tab for failed resource loads (404s)

### 3. Test Navigation
- Click all navigation links
- Use browser back/forward buttons
- Refresh pages to ensure they load correctly

## 🛠️ Troubleshooting Commands

### Check if mod_rewrite is working:
Create a test file `test-rewrite.php`:
```php
<?php
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "mod_rewrite is enabled";
    } else {
        echo "mod_rewrite is NOT enabled";
    }
} else {
    echo "Cannot determine if mod_rewrite is enabled";
}
?>
```

### Check .htaccess syntax:
Most hosting providers have .htaccess syntax checkers in their control panels.

## 📞 Getting Help

If issues persist:
1. **Check hosting documentation** for Next.js or SPA deployment
2. **Contact hosting support** - mention you need mod_rewrite enabled
3. **Check hosting error logs** for specific error messages
4. **Test with a simple .htaccess** to isolate issues

## 🔄 Re-deployment Process

When you make changes:
1. Run `npm run build` locally
2. Run `node deployment-check.js` to verify
3. Upload only changed files from `out` directory
4. Clear any hosting-level caches
5. Test the updated functionality

## 📈 Performance Optimization

After successful deployment:
- Enable Gzip compression (included in .htaccess)
- Set up CDN if available through your hosting
- Monitor Core Web Vitals
- Consider enabling browser caching (included in .htaccess)
