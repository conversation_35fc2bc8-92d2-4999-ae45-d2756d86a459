"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[711],{43:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(2149);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},392:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(1018).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},493:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},1018:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(2149);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:u,className:c="",children:s,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:n,strokeWidth:u?24*Number(a)/Number(o):a,className:i("lucide",c),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(s)?s:[s]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,l)=>{let{className:u,...c}=n;return(0,r.createElement)(a,{ref:l,iconNode:t,className:i("lucide-".concat(o(e)),u),...c})});return n.displayName="".concat(e),n}},3376:(e,t,n)=>{n.d(t,{i:()=>i});var r=n(2149),o=n(3425);function i({prop:e,defaultProp:t,onChange:n=()=>{}}){let[i,l]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[i]=n,l=r.useRef(i),a=(0,o.c)(t);return r.useEffect(()=>{l.current!==i&&(a(i),l.current=i)},[i,l,a]),n}({defaultProp:t,onChange:n}),a=void 0!==e,u=a?e:i,c=(0,o.c)(n);return[u,r.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&c(n)}else l(t)},[a,e,l,c])]}},3425:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2149);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},3940:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(2149),o=n(8081);function i(e,t=[]){let n=[],l=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return l.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(l,...t)]}},5113:(e,t,n)=>{n.d(t,{C1:()=>A,bL:()=>N});var r=n(2149),o=n(9264),i=n(3940),l=n(493),a=n(3376),u=n(43),c=n(5516),s=n(9572),d=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef({}),a=r.useRef(e),u=r.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=f(l.current);u.current="mounted"===c?e:"none"},[c]),(0,s.N)(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=f(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),(0,s.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=f(l.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(u.current=f(l.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),l="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),a=(0,o.s)(i.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||i.isPresent?r.cloneElement(l,{ref:a}):null};function f(e){return(null==e?void 0:e.animationName)||"none"}d.displayName="Presence";var p=n(9215),v=n(8081),m="Checkbox",[h,g]=(0,i.A)(m),[y,w]=h(m),b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:i,checked:u,defaultChecked:c,required:s,disabled:d,value:f="on",onCheckedChange:m,form:h,...g}=e,[w,b]=r.useState(null),x=(0,o.s)(t,e=>b(e)),E=r.useRef(!1),N=!w||h||!!w.closest("form"),[A=!1,T]=(0,a.i)({prop:u,defaultProp:c,onChange:m}),k=r.useRef(A);return r.useEffect(()=>{let e=null==w?void 0:w.form;if(e){let t=()=>T(k.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[w,T]),(0,v.jsxs)(y,{scope:n,state:A,disabled:d,children:[(0,v.jsx)(p.sG.button,{type:"button",role:"checkbox","aria-checked":C(A)?"mixed":A,"aria-required":s,"data-state":R(A),"data-disabled":d?"":void 0,disabled:d,value:f,...g,ref:x,onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(e.onClick,e=>{T(e=>!!C(e)||!e),N&&(E.current=e.isPropagationStopped(),E.current||e.stopPropagation())})}),N&&(0,v.jsx)(S,{control:w,bubbles:!E.current,name:i,value:f,checked:A,required:s,disabled:d,form:h,style:{transform:"translateX(-100%)"},defaultChecked:!C(c)&&c})]})});b.displayName=m;var x="CheckboxIndicator",E=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=w(x,n);return(0,v.jsx)(d,{present:r||C(i.state)||!0===i.state,children:(0,v.jsx)(p.sG.span,{"data-state":R(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});E.displayName=x;var S=e=>{let{control:t,checked:n,bubbles:o=!0,defaultChecked:i,...l}=e,a=r.useRef(null),s=(0,u.Z)(n),d=(0,c.X)(t);r.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(s!==n&&t){let r=new Event("click",{bubbles:o});e.indeterminate=C(n),t.call(e,!C(n)&&n),e.dispatchEvent(r)}},[s,n,o]);let f=r.useRef(!C(n)&&n);return(0,v.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=i?i:f.current,...l,tabIndex:-1,ref:a,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function C(e){return"indeterminate"===e}function R(e){return C(e)?"indeterminate":e?"checked":"unchecked"}var N=b,A=E},5516:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(2149),o=n(9572);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},6379:(e,t,n)=>{n.d(t,{UC:()=>rs,YJ:()=>rf,In:()=>ru,q7:()=>rv,VF:()=>rh,p4:()=>rm,JU:()=>rp,ZL:()=>rc,bL:()=>ri,wn:()=>ry,PP:()=>rg,wv:()=>rw,l9:()=>rl,WT:()=>ra,LM:()=>rd});var r,o,i=n(2149),l=n.t(i,2),a=n(4632);function u(e,[t,n]){return Math.min(n,Math.max(t,e))}var c=n(493),s=n(3940),d=n(9264),f=n(9907),p=n(8081),v=i.createContext(void 0),m=n(9215),h=n(3425),g="dismissableLayer.update",y=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=i.forwardRef((e,t)=>{var n,o;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:s,onInteractOutside:f,onDismiss:v,...w}=e,E=i.useContext(y),[S,C]=i.useState(null),R=null!==(o=null==S?void 0:S.ownerDocument)&&void 0!==o?o:null===(n=globalThis)||void 0===n?void 0:n.document,[,N]=i.useState({}),A=(0,d.s)(t,e=>C(e)),T=Array.from(E.layers),[k]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),L=T.indexOf(k),P=S?T.indexOf(S):-1,M=E.layersWithOutsidePointerEventsDisabled.size>0,O=P>=L,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,h.c)(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){x("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!O||n||(null==u||u(e),null==f||f(e),e.defaultPrevented||null==v||v())},R),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,h.c)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&x("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==s||s(e),null==f||f(e),e.defaultPrevented||null==v||v())},R);return!function(e,t=globalThis?.document){let n=(0,h.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P===E.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},R),i.useEffect(()=>{if(S)return l&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),b(),()=>{l&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[S,R,l,E]),i.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),b())},[S,E]),i.useEffect(()=>{let e=()=>N({});return document.addEventListener(g,e),()=>document.removeEventListener(g,e)},[]),(0,p.jsx)(m.sG.div,{...w,ref:A,style:{pointerEvents:M?O?"auto":"none":void 0,...e.style},onFocusCapture:(0,c.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,c.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,c.m)(e.onPointerDownCapture,j.onPointerDownCapture)})});function b(){let e=new CustomEvent(g);document.dispatchEvent(e)}function x(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,m.hO)(i,l):i.dispatchEvent(l)}w.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(y),r=i.useRef(null),o=(0,d.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,p.jsx)(m.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var E=0;function S(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var C="focusScope.autoFocusOnMount",R="focusScope.autoFocusOnUnmount",N={bubbles:!1,cancelable:!0},A=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:l,...a}=e,[u,c]=i.useState(null),s=(0,h.c)(o),f=(0,h.c)(l),v=i.useRef(null),g=(0,d.s)(t,e=>c(e)),y=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let e=function(e){if(y.paused||!u)return;let t=e.target;u.contains(t)?v.current=t:L(v.current,{select:!0})},t=function(e){if(y.paused||!u)return;let t=e.relatedTarget;null===t||u.contains(t)||L(v.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&L(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,y.paused]),i.useEffect(()=>{if(u){P.add(y);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(C,N);u.addEventListener(C,s),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(L(r,{select:t}),document.activeElement!==n)return}(T(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&L(u))}return()=>{u.removeEventListener(C,s),setTimeout(()=>{let t=new CustomEvent(R,N);u.addEventListener(R,f),u.dispatchEvent(t),t.defaultPrevented||L(null!=e?e:document.body,{select:!0}),u.removeEventListener(R,f),P.remove(y)},0)}}},[u,s,f,y]);let w=i.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=T(e);return[k(t,e),k(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&L(i,{select:!0})):(e.preventDefault(),n&&L(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,p.jsx)(m.sG.div,{tabIndex:-1,...a,ref:g,onKeyDown:w})});function T(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function k(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function L(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}A.displayName="FocusScope";var P=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=M(e,t)).unshift(t)},remove(t){var n;null===(n=(e=M(e,t))[0])||void 0===n||n.resume()}}}();function M(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var O=n(9572),j=l["useId".toString()]||(()=>void 0),D=0;function I(e){let[t,n]=i.useState(j());return(0,O.N)(()=>{e||n(e=>e??String(D++))},[e]),e||(t?`radix-${t}`:"")}let W=["top","right","bottom","left"],F=Math.min,H=Math.max,B=Math.round,_=Math.floor,V=e=>({x:e,y:e}),z={left:"right",right:"left",bottom:"top",top:"bottom"},U={start:"end",end:"start"};function G(e,t){return"function"==typeof e?e(t):e}function K(e){return e.split("-")[0]}function X(e){return e.split("-")[1]}function Y(e){return"x"===e?"y":"x"}function q(e){return"y"===e?"height":"width"}let $=new Set(["top","bottom"]);function Z(e){return $.has(K(e))?"y":"x"}function J(e){return e.replace(/start|end/g,e=>U[e])}let Q=["left","right"],ee=["right","left"],et=["top","bottom"],en=["bottom","top"];function er(e){return e.replace(/left|right|bottom|top/g,e=>z[e])}function eo(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function ei(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function el(e,t,n){let r,{reference:o,floating:i}=e,l=Z(t),a=Y(Z(t)),u=q(a),c=K(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(X(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let ea=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=el(c,r,u),f=r,p={},v=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:h,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=h?h:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&v<=50&&(v++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=el(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function eu(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=G(t,e),v=eo(p),m=a[f?"floating"===d?"reference":"floating":d],h=ei(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=ei(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(h.top-b.top+v.top)/w.y,bottom:(b.bottom-h.bottom+v.bottom)/w.y,left:(h.left-b.left+v.left)/w.x,right:(b.right-h.right+v.right)/w.x}}function ec(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function es(e){return W.some(t=>e[t]>=0)}let ed=new Set(["left","top"]);async function ef(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=K(n),a=X(n),u="y"===Z(n),c=ed.has(l)?-1:1,s=i&&u?-1:1,d=G(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof v&&(p="end"===a?-1*v:v),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function ep(){return"undefined"!=typeof window}function ev(e){return eg(e)?(e.nodeName||"").toLowerCase():"#document"}function em(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eh(e){var t;return null==(t=(eg(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eg(e){return!!ep()&&(e instanceof Node||e instanceof em(e).Node)}function ey(e){return!!ep()&&(e instanceof Element||e instanceof em(e).Element)}function ew(e){return!!ep()&&(e instanceof HTMLElement||e instanceof em(e).HTMLElement)}function eb(e){return!!ep()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof em(e).ShadowRoot)}let ex=new Set(["inline","contents"]);function eE(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eO(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!ex.has(o)}let eS=new Set(["table","td","th"]),eC=[":popover-open",":modal"];function eR(e){return eC.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eN=["transform","translate","scale","rotate","perspective"],eA=["transform","translate","scale","rotate","perspective","filter"],eT=["paint","layout","strict","content"];function ek(e){let t=eL(),n=ey(e)?eO(e):e;return eN.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eA.some(e=>(n.willChange||"").includes(e))||eT.some(e=>(n.contain||"").includes(e))}function eL(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eP=new Set(["html","body","#document"]);function eM(e){return eP.has(ev(e))}function eO(e){return em(e).getComputedStyle(e)}function ej(e){return ey(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eD(e){if("html"===ev(e))return e;let t=e.assignedSlot||e.parentNode||eb(e)&&e.host||eh(e);return eb(t)?t.host:t}function eI(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eD(t);return eM(n)?t.ownerDocument?t.ownerDocument.body:t.body:ew(n)&&eE(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=em(o);if(i){let e=eW(l);return t.concat(l,l.visualViewport||[],eE(o)?o:[],e&&n?eI(e):[])}return t.concat(o,eI(o,[],n))}function eW(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eF(e){let t=eO(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=ew(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=B(n)!==i||B(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eH(e){return ey(e)?e:e.contextElement}function eB(e){let t=eH(e);if(!ew(t))return V(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eF(t),l=(i?B(n.width):n.width)/r,a=(i?B(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let e_=V(0);function eV(e){let t=em(e);return eL()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:e_}function ez(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eH(e),a=V(1);t&&(r?ey(r)&&(a=eB(r)):a=eB(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===em(l))&&o)?eV(l):V(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=em(l),t=r&&ey(r)?em(r):r,n=e,o=eW(n);for(;o&&r&&t!==n;){let e=eB(o),t=o.getBoundingClientRect(),r=eO(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=eW(n=em(o))}}return ei({width:d,height:f,x:c,y:s})}function eU(e,t){let n=ej(e).scrollLeft;return t?t.left+n:ez(eh(e)).left+n}function eG(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eU(e,r)),y:r.top+t.scrollTop}}let eK=new Set(["absolute","fixed"]);function eX(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=em(e),r=eh(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eL();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=eh(e),n=ej(e),r=e.ownerDocument.body,o=H(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=H(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eU(e),a=-n.scrollTop;return"rtl"===eO(r).direction&&(l+=H(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(eh(e));else if(ey(t))r=function(e,t){let n=ez(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ew(e)?eB(e):V(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=eV(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return ei(r)}function eY(e){return"static"===eO(e).position}function eq(e,t){if(!ew(e)||"fixed"===eO(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eh(e)===n&&(n=n.ownerDocument.body),n}function e$(e,t){var n;let r=em(e);if(eR(e))return r;if(!ew(e)){let t=eD(e);for(;t&&!eM(t);){if(ey(t)&&!eY(t))return t;t=eD(t)}return r}let o=eq(e,t);for(;o&&(n=o,eS.has(ev(n)))&&eY(o);)o=eq(o,t);return o&&eM(o)&&eY(o)&&!ek(o)?r:o||function(e){let t=eD(e);for(;ew(t)&&!eM(t);){if(ek(t))return t;if(eR(t))break;t=eD(t)}return null}(e)||r}let eZ=async function(e){let t=this.getOffsetParent||e$,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=ew(t),o=eh(t),i="fixed"===n,l=ez(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=V(0);if(r||!r&&!i){if(("body"!==ev(t)||eE(o))&&(a=ej(t)),r){let e=ez(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eU(o))}i&&!r&&o&&(u.x=eU(o));let c=!o||r||i?V(0):eG(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eJ={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=eh(r),a=!!t&&eR(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=V(1),s=V(0),d=ew(r);if((d||!d&&!i)&&(("body"!==ev(r)||eE(l))&&(u=ej(r)),ew(r))){let e=ez(r);c=eB(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?V(0):eG(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:eh,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?eR(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eI(e,[],!1).filter(e=>ey(e)&&"body"!==ev(e)),o=null,i="fixed"===eO(e).position,l=i?eD(e):e;for(;ey(l)&&!eM(l);){let t=eO(l),n=ek(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eK.has(o.position)||eE(l)&&!n&&function e(t,n){let r=eD(t);return!(r===n||!ey(r)||eM(r))&&("fixed"===eO(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eD(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eX(t,n,o);return e.top=H(r.top,e.top),e.right=F(r.right,e.right),e.bottom=F(r.bottom,e.bottom),e.left=H(r.left,e.left),e},eX(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:e$,getElementRects:eZ,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eF(e);return{width:t,height:n}},getScale:eB,isElement:ey,isRTL:function(e){return"rtl"===eO(e).direction}};function eQ(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e0=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=G(e,t)||{};if(null==c)return{};let d=eo(s),f={x:n,y:r},p=Y(Z(o)),v=q(p),m=await l.getDimensions(c),h="y"===p,g=h?"clientHeight":"clientWidth",y=i.reference[v]+i.reference[p]-f[p]-i.floating[v],w=f[p]-i.reference[p],b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),x=b?b[g]:0;x&&await (null==l.isElement?void 0:l.isElement(b))||(x=a.floating[g]||i.floating[v]);let E=x/2-m[v]/2-1,S=F(d[h?"top":"left"],E),C=F(d[h?"bottom":"right"],E),R=x-m[v]-C,N=x/2-m[v]/2+(y/2-w/2),A=H(S,F(N,R)),T=!u.arrow&&null!=X(o)&&N!==A&&i.reference[v]/2-(N<S?S:C)-m[v]/2<0,k=T?N<S?N-S:N-R:0;return{[p]:f[p]+k,data:{[p]:A,centerOffset:N-A-k,...T&&{alignmentOffset:k}},reset:T}}}),e1=(e,t,n)=>{let r=new Map,o={platform:eJ,...n},i={...o.platform,_c:r};return ea(e,t,{...o,platform:i})};var e2="undefined"!=typeof document?i.useLayoutEffect:function(){};function e9(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e9(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e9(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e5(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e4(e,t){let n=e5(e);return Math.round(t*n)/n}function e3(e){let t=i.useRef(e);return e2(()=>{t.current=e}),t}let e6=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e0({element:n.current,padding:r}).fn(t):{}:n?e0({element:n,padding:r}).fn(t):{}}}),e7=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await ef(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),e8=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=G(e,t),c={x:n,y:r},s=await eu(t,u),d=Z(K(o)),f=Y(d),p=c[f],v=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=H(n,F(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=v+s[e],r=v-s[t];v=H(n,F(v,r))}let m=a.fn({...t,[f]:p,[d]:v});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),te=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=G(e,t),s={x:n,y:r},d=Z(o),f=Y(d),p=s[f],v=s[d],m=G(a,t),h="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+h.mainAxis,n=i.reference[f]+i.reference[e]-h.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=ed.has(K(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:h.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?h.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[f]:p,[d]:v}}}}(e),options:[e,t]}),tt=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:v=!0,fallbackPlacements:m,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=G(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let b=K(a),x=Z(s),E=K(s)===s,S=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=m||(E||!y?[er(s)]:function(e){let t=er(e);return[J(e),t,J(t)]}(s)),R="none"!==g;!m&&R&&C.push(...function(e,t,n,r){let o=X(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?ee:Q;return t?Q:ee;case"left":case"right":return t?et:en;default:return[]}}(K(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(J)))),i}(s,y,g,S));let N=[s,...C],A=await eu(t,w),T=[],k=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&T.push(A[b]),v){let e=function(e,t,n){void 0===n&&(n=!1);let r=X(e),o=Y(Z(e)),i=q(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=er(l)),[l,er(l)]}(a,c,S);T.push(A[e[0]],A[e[1]])}if(k=[...k,{placement:a,overflows:T}],!T.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=N[e];if(t&&("alignment"!==v||x===Z(t)||k.every(e=>e.overflows[0]>0&&Z(e.placement)===x)))return{data:{index:e,overflows:k},reset:{placement:t}};let n=null==(i=k.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(h){case"bestFit":{let e=null==(l=k.filter(e=>{if(R){let t=Z(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),tn=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=t,{apply:s=()=>{},...d}=G(e,t),f=await eu(t,d),p=K(l),v=X(l),m="y"===Z(l),{width:h,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===v?"top":"bottom");let y=g-f.top-f.bottom,w=h-f.left-f.right,b=F(g-f[o],y),x=F(h-f[i],w),E=!t.middlewareData.shift,S=b,C=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(S=y),E&&!v){let e=H(f.left,0),t=H(f.right,0),n=H(f.top,0),r=H(f.bottom,0);m?C=h-2*(0!==e||0!==t?e+t:H(f.left,f.right)):S=g-2*(0!==n||0!==r?n+r:H(f.top,f.bottom))}await s({...t,availableWidth:C,availableHeight:S});let R=await u.getDimensions(c.floating);return h!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),tr=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=G(e,t);switch(r){case"referenceHidden":{let e=ec(await eu(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:es(e)}}}case"escaped":{let e=ec(await eu(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:es(e)}}}default:return{}}}}}(e),options:[e,t]}),to=(e,t)=>({...e6(e),options:[e,t]});var ti=i.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,p.jsx)(m.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,p.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ti.displayName="Arrow";var tl=n(5516),ta="Popper",[tu,tc]=(0,s.A)(ta),[ts,td]=tu(ta),tf=e=>{let{__scopePopper:t,children:n}=e,[r,o]=i.useState(null);return(0,p.jsx)(ts,{scope:t,anchor:r,onAnchorChange:o,children:n})};tf.displayName=ta;var tp="PopperAnchor",tv=i.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,l=td(tp,n),a=i.useRef(null),u=(0,d.s)(t,a);return i.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,p.jsx)(m.sG.div,{...o,ref:u})});tv.displayName=tp;var tm="PopperContent",[th,tg]=tu(tm),ty=i.forwardRef((e,t)=>{var n,r,o,l,u,c,s,f;let{__scopePopper:v,side:g="bottom",sideOffset:y=0,align:w="center",alignOffset:b=0,arrowPadding:x=0,avoidCollisions:E=!0,collisionBoundary:S=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:N=!1,updatePositionStrategy:A="optimized",onPlaced:T,...k}=e,L=td(tm,v),[P,M]=i.useState(null),j=(0,d.s)(t,e=>M(e)),[D,I]=i.useState(null),W=(0,tl.X)(D),B=null!==(s=null==W?void 0:W.width)&&void 0!==s?s:0,V=null!==(f=null==W?void 0:W.height)&&void 0!==f?f:0,z="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},U=Array.isArray(S)?S:[S],G=U.length>0,K={padding:z,boundary:U.filter(tE),altBoundary:G},{refs:X,floatingStyles:Y,placement:q,isPositioned:$,middlewareData:Z}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:l,floating:u}={},transform:c=!0,whileElementsMounted:s,open:d}=e,[f,p]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,m]=i.useState(r);e9(v,r)||m(r);let[h,g]=i.useState(null),[y,w]=i.useState(null),b=i.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=i.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=l||h,S=u||y,C=i.useRef(null),R=i.useRef(null),N=i.useRef(f),A=null!=s,T=e3(s),k=e3(o),L=e3(d),P=i.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:v};k.current&&(e.platform=k.current),e1(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};M.current&&!e9(N.current,t)&&(N.current=t,a.flushSync(()=>{p(t)}))})},[v,t,n,k,L]);e2(()=>{!1===d&&N.current.isPositioned&&(N.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let M=i.useRef(!1);e2(()=>(M.current=!0,()=>{M.current=!1}),[]),e2(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(T.current)return T.current(E,S,P);P()}},[E,S,P,T,A]);let O=i.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),j=i.useMemo(()=>({reference:E,floating:S}),[E,S]),D=i.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=e4(j.floating,f.x),r=e4(j.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...e5(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,j.floating,f.x,f.y]);return i.useMemo(()=>({...f,update:P,refs:O,elements:j,floatingStyles:D}),[f,P,O,j,D])}({strategy:"fixed",placement:g+("center"!==w?"-"+w:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eH(e),d=i||l?[...s?eI(s):[],...eI(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=eh(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let v=_(d),m=_(o.clientWidth-(s+f)),h={rootMargin:-v+"px "+-m+"px "+-_(o.clientHeight-(d+p))+"px "+-_(s)+"px",threshold:H(0,F(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eQ(c,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...h,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,h)}r.observe(e)}(!0),i}(s,n):null,p=-1,v=null;a&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&v&&(v.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),s&&!c&&v.observe(s),v.observe(t));let m=c?ez(e):null;return c&&function t(){let r=ez(e);m&&!eQ(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=v)||e.disconnect(),v=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:L.anchor},middleware:[e7({mainAxis:y+V,alignmentAxis:b}),E&&e8({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?te():void 0,...K}),E&&tt({...K}),tn({...K,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&to({element:D,padding:x}),tS({arrowWidth:B,arrowHeight:V}),N&&tr({strategy:"referenceHidden",...K})]}),[J,Q]=tC(q),ee=(0,h.c)(T);(0,O.N)(()=>{$&&(null==ee||ee())},[$,ee]);let et=null===(n=Z.arrow)||void 0===n?void 0:n.x,en=null===(r=Z.arrow)||void 0===r?void 0:r.y,er=(null===(o=Z.arrow)||void 0===o?void 0:o.centerOffset)!==0,[eo,ei]=i.useState();return(0,O.N)(()=>{P&&ei(window.getComputedStyle(P).zIndex)},[P]),(0,p.jsx)("div",{ref:X.setFloating,"data-radix-popper-content-wrapper":"",style:{...Y,transform:$?Y.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eo,"--radix-popper-transform-origin":[null===(l=Z.transformOrigin)||void 0===l?void 0:l.x,null===(u=Z.transformOrigin)||void 0===u?void 0:u.y].join(" "),...(null===(c=Z.hide)||void 0===c?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,p.jsx)(th,{scope:v,placedSide:J,onArrowChange:I,arrowX:et,arrowY:en,shouldHideArrow:er,children:(0,p.jsx)(m.sG.div,{"data-side":J,"data-align":Q,...k,ref:j,style:{...k.style,animation:$?void 0:"none"}})})})});ty.displayName=tm;var tw="PopperArrow",tb={top:"bottom",right:"left",bottom:"top",left:"right"},tx=i.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tg(tw,n),i=tb[o.placedSide];return(0,p.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,p.jsx)(ti,{...r,ref:t,style:{...r.style,display:"block"}})})});function tE(e){return null!==e}tx.displayName=tw;var tS=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,v]=tC(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!==(i=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(l=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?m:"".concat(h,"px"),w="".concat(-f,"px")):"top"===p?(y=s?m:"".concat(h,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?m:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function tC(e){let[t,n="center"]=e.split("-");return[t,n]}var tR=i.forwardRef((e,t)=>{var n,r;let{container:o,...l}=e,[u,c]=i.useState(!1);(0,O.N)(()=>c(!0),[]);let s=o||u&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return s?a.createPortal((0,p.jsx)(m.sG.div,{...l,ref:t}),s):null});tR.displayName="Portal";var tN=n(3376),tA=n(43),tT=i.forwardRef((e,t)=>(0,p.jsx)(m.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));tT.displayName="VisuallyHidden";var tk=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tL=new WeakMap,tP=new WeakMap,tM={},tO=0,tj=function(e){return e&&(e.host||tj(e.parentNode))},tD=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tj(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tM[n]||(tM[n]=new WeakMap);var i=tM[n],l=[],a=new Set,u=new Set(o),c=function(e){!(!e||a.has(e))&&(a.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!(!e||u.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(a.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(tL.get(e)||0)+1,c=(i.get(e)||0)+1;tL.set(e,u),i.set(e,c),l.push(e),1===u&&o&&tP.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),a.clear(),tO++,function(){l.forEach(function(e){var t=tL.get(e)-1,o=i.get(e)-1;tL.set(e,t),i.set(e,o),t||(tP.has(e)||e.removeAttribute(r),tP.delete(e)),o||e.removeAttribute(n)}),--tO||(tL=new WeakMap,tL=new WeakMap,tP=new WeakMap,tM={})}},tI=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||tk(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),tD(r,o,n,"aria-hidden")):function(){return null}},tW=function(){return(tW=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tF(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var tH=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),tB="width-before-scroll-bar";function t_(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tV="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,tz=new WeakMap;function tU(e){return e}var tG=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=tU),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return i.options=tW({async:!0,ssr:!1},e),i}(),tK=function(){},tX=i.forwardRef(function(e,t){var n,r,o,l,a=i.useRef(null),u=i.useState({onScrollCapture:tK,onWheelCapture:tK,onTouchMoveCapture:tK}),c=u[0],s=u[1],d=e.forwardProps,f=e.children,p=e.className,v=e.removeScrollBar,m=e.enabled,h=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,b=e.inert,x=e.allowPinchZoom,E=e.as,S=e.gapMode,C=tF(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[a,t],r=function(e){return n.forEach(function(t){return t_(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,tV(function(){var e=tz.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||t_(e,null)}),r.forEach(function(e){t.has(e)||t_(e,o)})}tz.set(l,n)},[n]),l),N=tW(tW({},C),c);return i.createElement(i.Fragment,null,m&&i.createElement(g,{sideCar:tG,removeScrollBar:v,shards:h,noRelative:y,noIsolation:w,inert:b,setCallbacks:s,allowPinchZoom:!!x,lockRef:a,gapMode:S}),d?i.cloneElement(i.Children.only(f),tW(tW({},N),{ref:R})):i.createElement(void 0===E?"div":E,tW({},N,{className:p,ref:R}),f))});tX.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tX.classNames={fullWidth:tB,zeroRight:tH};var tY=function(e){var t=e.sideCar,n=tF(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,tW({},n))};tY.isSideCarExport=!0;var tq=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},t$=function(){var e=tq();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tZ=function(){var e=t$();return function(t){return e(t.styles,t.dynamic),null}},tJ={left:0,top:0,right:0,gap:0},tQ=function(e){return parseInt(e||"",10)||0},t0=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tQ(n),tQ(r),tQ(o)]},t1=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tJ;var t=t0(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},t2=tZ(),t9="data-scroll-locked",t5=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(t9,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tH," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tB," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(tH," .").concat(tH," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(tB," .").concat(tB," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t9,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},t4=function(){var e=parseInt(document.body.getAttribute(t9)||"0",10);return isFinite(e)?e:0},t3=function(){i.useEffect(function(){return document.body.setAttribute(t9,(t4()+1).toString()),function(){var e=t4()-1;e<=0?document.body.removeAttribute(t9):document.body.setAttribute(t9,e.toString())}},[])},t6=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;t3();var l=i.useMemo(function(){return t1(o)},[o]);return i.createElement(t2,{styles:t5(l,!t,o,n?"":"!important")})},t7=!1;if("undefined"!=typeof window)try{var t8=Object.defineProperty({},"passive",{get:function(){return t7=!0,!0}});window.addEventListener("test",t8,t8),window.removeEventListener("test",t8,t8)}catch(e){t7=!1}var ne=!!t7&&{passive:!1},nt=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},nn=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),nr(e,r)){var o=no(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},nr=function(e,t){return"v"===e?nt(t,"overflowY"):nt(t,"overflowX")},no=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ni=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var v=no(e,u),m=v[0],h=v[1]-v[2]-l*m;(m||h)&&nr(e,u)&&(f+=h,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},nl=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},na=function(e){return[e.deltaX,e.deltaY]},nu=function(e){return e&&"current"in e?e.current:e},nc=0,ns=[];let nd=(tG.useMedium(function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(nc++)[0],l=i.useState(tZ)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(nu),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=nl(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=nn(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=nn(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return ni(p,t,e,"h"===p?u:c,!0)},[]),c=i.useCallback(function(e){if(ns.length&&ns[ns.length-1]===l){var n="deltaY"in e?na(e):nl(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(nu).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=i.useCallback(function(e){n.current=nl(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,na(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,nl(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return ns.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,ne),document.addEventListener("touchmove",c,ne),document.addEventListener("touchstart",d,ne),function(){ns=ns.filter(function(e){return e!==l}),document.removeEventListener("wheel",c,ne),document.removeEventListener("touchmove",c,ne),document.removeEventListener("touchstart",d,ne)}},[]);var v=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?i.createElement(t6,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),tY);var nf=i.forwardRef(function(e,t){return i.createElement(tX,tW({},e,{ref:t,sideCar:nd}))});nf.classNames=tX.classNames;var np=[" ","Enter","ArrowUp","ArrowDown"],nv=[" ","Enter"],nm="Select",[nh,ng,ny]=function(e){let t=e+"CollectionProvider",[n,r]=(0,s.A)(t),[o,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=i.useRef(null),l=i.useRef(new Map).current;return(0,p.jsx)(o,{scope:t,itemMap:l,collectionRef:r,children:n})};a.displayName=t;let u=e+"CollectionSlot",c=i.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(u,n),i=(0,d.s)(t,o.collectionRef);return(0,p.jsx)(f.DX,{ref:i,children:r})});c.displayName=u;let v=e+"CollectionItemSlot",m="data-radix-collection-item",h=i.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=i.useRef(null),u=(0,d.s)(t,a),c=l(v,n);return i.useEffect(()=>(c.itemMap.set(a,{ref:a,...o}),()=>void c.itemMap.delete(a))),(0,p.jsx)(f.DX,{[m]:"",ref:u,children:r})});return h.displayName=v,[{Provider:a,Slot:c,ItemSlot:h},function(t){let n=l(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(nm),[nw,nb]=(0,s.A)(nm,[ny,tc]),nx=tc(),[nE,nS]=nw(nm),[nC,nR]=nw(nm),nN=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:s,name:d,autoComplete:f,disabled:m,required:h,form:g}=e,y=nx(t),[w,b]=i.useState(null),[x,E]=i.useState(null),[S,C]=i.useState(!1),R=function(e){let t=i.useContext(v);return e||t||"ltr"}(s),[N=!1,A]=(0,tN.i)({prop:r,defaultProp:o,onChange:l}),[T,k]=(0,tN.i)({prop:a,defaultProp:u,onChange:c}),L=i.useRef(null),P=!w||g||!!w.closest("form"),[M,O]=i.useState(new Set),j=Array.from(M).map(e=>e.props.value).join(";");return(0,p.jsx)(tf,{...y,children:(0,p.jsxs)(nE,{required:h,scope:t,trigger:w,onTriggerChange:b,valueNode:x,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:I(),value:T,onValueChange:k,open:N,onOpenChange:A,dir:R,triggerPointerDownPosRef:L,disabled:m,children:[(0,p.jsx)(nh.Provider,{scope:t,children:(0,p.jsx)(nC,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:i.useCallback(e=>{O(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),P?(0,p.jsxs)(rn,{"aria-hidden":!0,required:h,tabIndex:-1,name:d,autoComplete:f,value:T,onChange:e=>k(e.target.value),disabled:m,form:g,children:[void 0===T?(0,p.jsx)("option",{value:""}):null,Array.from(M)]},j):null]})})};nN.displayName=nm;var nA="SelectTrigger",nT=i.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...o}=e,l=nx(n),a=nS(nA,n),u=a.disabled||r,s=(0,d.s)(t,a.onTriggerChange),f=ng(n),v=i.useRef("touch"),[h,g,y]=rr(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===a.value),r=ro(t,e,n);void 0!==r&&a.onValueChange(r.value)}),w=e=>{u||(a.onOpenChange(!0),y()),e&&(a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,p.jsx)(tv,{asChild:!0,...l,children:(0,p.jsx)(m.sG.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":rt(a.value)?"":void 0,...o,ref:s,onClick:(0,c.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==v.current&&w(e)}),onPointerDown:(0,c.m)(o.onPointerDown,e=>{v.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,c.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&np.includes(e.key)&&(w(),e.preventDefault())})})})});nT.displayName=nA;var nk="SelectValue",nL=i.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=nS(nk,n),{onValueNodeHasChildrenChange:c}=u,s=void 0!==i,f=(0,d.s)(t,u.onValueNodeChange);return(0,O.N)(()=>{c(s)},[c,s]),(0,p.jsx)(m.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:rt(u.value)?(0,p.jsx)(p.Fragment,{children:l}):i})});nL.displayName=nk;var nP=i.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,p.jsx)(m.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});nP.displayName="SelectIcon";var nM=e=>(0,p.jsx)(tR,{asChild:!0,...e});nM.displayName="SelectPortal";var nO="SelectContent",nj=i.forwardRef((e,t)=>{let n=nS(nO,e.__scopeSelect),[r,o]=i.useState();return((0,O.N)(()=>{o(new DocumentFragment)},[]),n.open)?(0,p.jsx)(nW,{...e,ref:t}):r?a.createPortal((0,p.jsx)(nD,{scope:e.__scopeSelect,children:(0,p.jsx)(nh.Slot,{scope:e.__scopeSelect,children:(0,p.jsx)("div",{children:e.children})})}),r):null});nj.displayName=nO;var[nD,nI]=nw(nO),nW=i.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:l,onPointerDownOutside:a,side:u,sideOffset:s,align:v,alignOffset:m,arrowPadding:h,collisionBoundary:g,collisionPadding:y,sticky:b,hideWhenDetached:x,avoidCollisions:C,...R}=e,N=nS(nO,n),[T,k]=i.useState(null),[L,P]=i.useState(null),M=(0,d.s)(t,e=>k(e)),[O,j]=i.useState(null),[D,I]=i.useState(null),W=ng(n),[F,H]=i.useState(!1),B=i.useRef(!1);i.useEffect(()=>{if(T)return tI(T)},[T]),i.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:S()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:S()),E++,()=>{1===E&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),E--}},[]);let _=i.useCallback(e=>{let[t,...n]=W().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&L&&(L.scrollTop=0),n===r&&L&&(L.scrollTop=L.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[W,L]),V=i.useCallback(()=>_([O,T]),[_,O,T]);i.useEffect(()=>{F&&V()},[F,V]);let{onOpenChange:z,triggerPointerDownPosRef:U}=N;i.useEffect(()=>{if(T){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=U.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=U.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():T.contains(n.target)||z(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[T,z,U]),i.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[G,K]=rr(e=>{let t=W().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=ro(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),X=i.useCallback((e,t,n)=>{let r=!B.current&&!n;(void 0!==N.value&&N.value===t||r)&&(j(e),r&&(B.current=!0))},[N.value]),Y=i.useCallback(()=>null==T?void 0:T.focus(),[T]),q=i.useCallback((e,t,n)=>{let r=!B.current&&!n;(void 0!==N.value&&N.value===t||r)&&I(e)},[N.value]),$="popper"===r?nH:nF,Z=$===nH?{side:u,sideOffset:s,align:v,alignOffset:m,arrowPadding:h,collisionBoundary:g,collisionPadding:y,sticky:b,hideWhenDetached:x,avoidCollisions:C}:{};return(0,p.jsx)(nD,{scope:n,content:T,viewport:L,onViewportChange:P,itemRefCallback:X,selectedItem:O,onItemLeave:Y,itemTextRefCallback:q,focusSelectedItem:V,selectedItemText:D,position:r,isPositioned:F,searchRef:G,children:(0,p.jsx)(nf,{as:f.DX,allowPinchZoom:!0,children:(0,p.jsx)(A,{asChild:!0,trapped:N.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,c.m)(o,e=>{var t;null===(t=N.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,p.jsx)(w,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>N.onOpenChange(!1),children:(0,p.jsx)($,{role:"listbox",id:N.contentId,"data-state":N.open?"open":"closed",dir:N.dir,onContextMenu:e=>e.preventDefault(),...R,...Z,onPlaced:()=>H(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,c.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||K(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=W().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>_(t)),e.preventDefault()}})})})})})})});nW.displayName="SelectContentImpl";var nF=i.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,l=nS(nO,n),a=nI(nO,n),[c,s]=i.useState(null),[f,v]=i.useState(null),h=(0,d.s)(t,e=>v(e)),g=ng(n),y=i.useRef(!1),w=i.useRef(!0),{viewport:b,selectedItem:x,selectedItemText:E,focusSelectedItem:S}=a,C=i.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&f&&b&&x&&E){let e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==l.dir){let r=o.left-t.left,i=n.left-r,l=e.left-i,a=e.width+l,s=Math.max(a,t.width),d=u(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=a+"px",c.style.left=d+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,l=window.innerWidth-e.right-i,a=e.width+l,s=Math.max(a,t.width),d=u(i,[10,Math.max(10,window.innerWidth-10-s)]);c.style.minWidth=a+"px",c.style.right=d+"px"}let i=g(),a=window.innerHeight-20,s=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),h=p+v+s+parseInt(d.paddingBottom,10)+m,w=Math.min(5*x.offsetHeight,h),S=window.getComputedStyle(b),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),N=e.top+e.height/2-10,A=x.offsetHeight/2,T=p+v+(x.offsetTop+A);if(T<=N){let e=i.length>0&&x===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(a-N,A+(e?R:0)+(f.clientHeight-b.offsetTop-b.offsetHeight)+m);c.style.height=T+t+"px"}else{let e=i.length>0&&x===i[0].ref.current;c.style.top="0px";let t=Math.max(N,p+b.offsetTop+(e?C:0)+A);c.style.height=t+(h-T)+"px",b.scrollTop=T-N+b.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=w+"px",c.style.maxHeight=a+"px",null==r||r(),requestAnimationFrame(()=>y.current=!0)}},[g,l.trigger,l.valueNode,c,f,b,x,E,l.dir,r]);(0,O.N)(()=>C(),[C]);let[R,N]=i.useState();(0,O.N)(()=>{f&&N(window.getComputedStyle(f).zIndex)},[f]);let A=i.useCallback(e=>{e&&!0===w.current&&(C(),null==S||S(),w.current=!1)},[C,S]);return(0,p.jsx)(nB,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:y,onScrollButtonChange:A,children:(0,p.jsx)("div",{ref:s,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,p.jsx)(m.sG.div,{...o,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});nF.displayName="SelectItemAlignedPosition";var nH=i.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=nx(n);return(0,p.jsx)(ty,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});nH.displayName="SelectPopperPosition";var[nB,n_]=nw(nO,{}),nV="SelectViewport",nz=i.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,l=nI(nV,n),a=n_(nV,n),u=(0,d.s)(t,l.onViewportChange),s=i.useRef(0);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,p.jsx)(nh.Slot,{scope:n,children:(0,p.jsx)(m.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,c.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=a;if((null==r?void 0:r.current)&&n){let e=Math.abs(s.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}s.current=t.scrollTop})})})]})});nz.displayName=nV;var nU="SelectGroup",[nG,nK]=nw(nU),nX=i.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=I();return(0,p.jsx)(nG,{scope:n,id:o,children:(0,p.jsx)(m.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});nX.displayName=nU;var nY="SelectLabel",nq=i.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nK(nY,n);return(0,p.jsx)(m.sG.div,{id:o.id,...r,ref:t})});nq.displayName=nY;var n$="SelectItem",[nZ,nJ]=nw(n$),nQ=i.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:l,...a}=e,u=nS(n$,n),s=nI(n$,n),f=u.value===r,[v,h]=i.useState(null!=l?l:""),[g,y]=i.useState(!1),w=(0,d.s)(t,e=>{var t;return null===(t=s.itemRefCallback)||void 0===t?void 0:t.call(s,e,r,o)}),b=I(),x=i.useRef("touch"),E=()=>{o||(u.onValueChange(r),u.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,p.jsx)(nZ,{scope:n,value:r,disabled:o,textId:b,isSelected:f,onItemTextChange:i.useCallback(e=>{h(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,p.jsx)(nh.ItemSlot,{scope:n,value:r,disabled:o,textValue:v,children:(0,p.jsx)(m.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":g?"":void 0,"aria-selected":f&&g,"data-state":f?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...a,ref:w,onFocus:(0,c.m)(a.onFocus,()=>y(!0)),onBlur:(0,c.m)(a.onBlur,()=>y(!1)),onClick:(0,c.m)(a.onClick,()=>{"mouse"!==x.current&&E()}),onPointerUp:(0,c.m)(a.onPointerUp,()=>{"mouse"===x.current&&E()}),onPointerDown:(0,c.m)(a.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,c.m)(a.onPointerMove,e=>{if(x.current=e.pointerType,o){var t;null===(t=s.onItemLeave)||void 0===t||t.call(s)}else"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,c.m)(a.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=s.onItemLeave)||void 0===t||t.call(s)}}),onKeyDown:(0,c.m)(a.onKeyDown,e=>{var t;((null===(t=s.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(nv.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});nQ.displayName=n$;var n0="SelectItemText",n1=i.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,...l}=e,u=nS(n0,n),c=nI(n0,n),s=nJ(n0,n),f=nR(n0,n),[v,h]=i.useState(null),g=(0,d.s)(t,e=>h(e),s.onItemTextChange,e=>{var t;return null===(t=c.itemTextRefCallback)||void 0===t?void 0:t.call(c,e,s.value,s.disabled)}),y=null==v?void 0:v.textContent,w=i.useMemo(()=>(0,p.jsx)("option",{value:s.value,disabled:s.disabled,children:y},s.value),[s.disabled,s.value,y]),{onNativeOptionAdd:b,onNativeOptionRemove:x}=f;return(0,O.N)(()=>(b(w),()=>x(w)),[b,x,w]),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(m.sG.span,{id:s.textId,...l,ref:g}),s.isSelected&&u.valueNode&&!u.valueNodeHasChildren?a.createPortal(l.children,u.valueNode):null]})});n1.displayName=n0;var n2="SelectItemIndicator",n9=i.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return nJ(n2,n).isSelected?(0,p.jsx)(m.sG.span,{"aria-hidden":!0,...r,ref:t}):null});n9.displayName=n2;var n5="SelectScrollUpButton",n4=i.forwardRef((e,t)=>{let n=nI(n5,e.__scopeSelect),r=n_(n5,e.__scopeSelect),[o,l]=i.useState(!1),a=(0,d.s)(t,r.onScrollButtonChange);return(0,O.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,p.jsx)(n7,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});n4.displayName=n5;var n3="SelectScrollDownButton",n6=i.forwardRef((e,t)=>{let n=nI(n3,e.__scopeSelect),r=n_(n3,e.__scopeSelect),[o,l]=i.useState(!1),a=(0,d.s)(t,r.onScrollButtonChange);return(0,O.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,p.jsx)(n7,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});n6.displayName=n3;var n7=i.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,l=nI("SelectScrollButton",n),a=i.useRef(null),u=ng(n),s=i.useCallback(()=>{null!==a.current&&(window.clearInterval(a.current),a.current=null)},[]);return i.useEffect(()=>()=>s(),[s]),(0,O.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,p.jsx)(m.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,c.m)(o.onPointerDown,()=>{null===a.current&&(a.current=window.setInterval(r,50))}),onPointerMove:(0,c.m)(o.onPointerMove,()=>{var e;null===(e=l.onItemLeave)||void 0===e||e.call(l),null===a.current&&(a.current=window.setInterval(r,50))}),onPointerLeave:(0,c.m)(o.onPointerLeave,()=>{s()})})}),n8=i.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,p.jsx)(m.sG.div,{"aria-hidden":!0,...r,ref:t})});n8.displayName="SelectSeparator";var re="SelectArrow";function rt(e){return""===e||void 0===e}i.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=nx(n),i=nS(re,n),l=nI(re,n);return i.open&&"popper"===l.position?(0,p.jsx)(tx,{...o,...r,ref:t}):null}).displayName=re;var rn=i.forwardRef((e,t)=>{let{value:n,...r}=e,o=i.useRef(null),l=(0,d.s)(t,o),a=(0,tA.Z)(n);return i.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[a,n]),(0,p.jsx)(tT,{asChild:!0,children:(0,p.jsx)("select",{...r,ref:l,defaultValue:n})})});function rr(e){let t=(0,h.c)(e),n=i.useRef(""),r=i.useRef(0),o=i.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),l=i.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,l]}function ro(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}rn.displayName="BubbleSelect";var ri=nN,rl=nT,ra=nL,ru=nP,rc=nM,rs=nj,rd=nz,rf=nX,rp=nq,rv=nQ,rm=n1,rh=n9,rg=n4,ry=n6,rw=n8},6722:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(1018).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6801:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(1018).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},9572:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2149),o=globalThis?.document?r.useLayoutEffect:()=>{}}}]);