import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import Link from "next/link"
import Image from "next/image"
import { ChevronDown, Search } from "lucide-react"
import { Suspense } from "react"
import Footer from "@/components/footer"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "MK Academia - Education Research and Lab Management",
  description:
    "MK Academia offers high-quality educational, research and Lab Management services by our panel of experts",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {/* Top Bar */}
        <div className="bg-[#0a2a5e] text-white py-2 px-4">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-2">
            <div className="flex items-center gap-2">
              <p className="text-xs sm:text-sm text-center sm:text-left">NEED HELP? TALK TO AN EXPERT</p>
            </div>
            <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-4">
              <a href="mailto:<EMAIL>" className="text-xs sm:text-sm flex items-center gap-2 hover:text-[#f5a623] transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-mail flex-shrink-0"
                >
                  <rect width="20" height="16" x="2" y="4" rx="2" />
                  <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                </svg>
                <span className="hidden sm:inline"><EMAIL></span>
                <span className="sm:hidden">Email</span>
              </a>
              <div className="flex items-center gap-2">
                <p className="text-xs hidden lg:block">Follow Us</p>
                <div className="flex gap-1.5">
                  <a href="#" className="bg-[#0077b5] rounded-full p-1.5 flex items-center justify-center w-7 h-7 hover:scale-110 transition-transform" aria-label="LinkedIn">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      stroke="currentColor"
                      strokeWidth="0"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                      <rect x="2" y="9" width="4" height="12"></rect>
                      <circle cx="4" cy="4" r="2"></circle>
                    </svg>
                  </a>
                  <a href="#" className="bg-[#1DA1F2] rounded-full p-1.5 flex items-center justify-center w-7 h-7 hover:scale-110 transition-transform" aria-label="Twitter">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      stroke="currentColor"
                      strokeWidth="0"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                    </svg>
                  </a>
                  <a href="#" className="bg-[#3b5998] rounded-full p-1.5 flex items-center justify-center w-7 h-7 hover:scale-110 transition-transform" aria-label="Facebook">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      stroke="currentColor"
                      strokeWidth="0"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                    </svg>
                  </a>
                  <a href="#" className="bg-[#E1306C] rounded-full p-1.5 flex items-center justify-center w-7 h-7 hover:scale-110 transition-transform" aria-label="Instagram">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      stroke="currentColor"
                      strokeWidth="0"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                      <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                      <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                    </svg>
                  </a>
                  <a href="#" className="bg-[#FFFC00] rounded-full p-1.5 flex items-center justify-center w-7 h-7 hover:scale-110 transition-transform" aria-label="Snapchat">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="black"
                      stroke="black"
                      strokeWidth="0"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M12 2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2zm0 2a8 8 0 1 0 0 16 8 8 0 0 0 0-16zm0 6a2 2 0 1 1 0 4 2 2 0 0 1 0-4z"></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="bg-white py-3 px-4 shadow-md sticky top-0 z-50">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center">
              {/* Logo */}
              <div className="flex items-center">
                <Link href="/" className="flex items-center">
                  <Image
                    src="/mk-logo.jpg"
                    alt="MK Academia Logo"
                    width={50}
                    height={50}
                    className="mr-2 sm:mr-3"
                  />
                  <span className="text-[#0a2a5e] font-semibold text-sm sm:text-base lg:text-lg">
                    MK ACADEMIA & LAB
                  </span>
                </Link>
              </div>

              {/* Desktop Navigation */}
              <div className="hidden lg:flex items-center space-x-6">
                <Link href="/" className="text-[#0a2a5e] font-medium hover:text-[#f5a623] transition-colors">
                  Home
                </Link>
                <div className="relative group">
                  <Link
                    href="/services"
                    className="text-[#0a2a5e] font-medium flex items-center group-hover:text-[#f5a623] transition-colors"
                  >
                    Services <ChevronDown className="ml-1 h-4 w-4" />
                  </Link>
                  <div className="absolute left-0 mt-0 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10 border border-gray-200">
                    <div className="py-2">
                      <Link
                        href="/services"
                        className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                      >
                        All Services
                      </Link>
                      <Link
                        href="/services/medical-research-support"
                        className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                      >
                        Medical Research Support
                      </Link>
                      <Link
                        href="/services/non-medical-research-support"
                        className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                      >
                        Non-Medical Research Support
                      </Link>
                      <Link
                        href="/services/brain-tumor-consultation"
                        className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                      >
                        Brain Tumor Consultation
                      </Link>
                      <Link
                        href="/services/statistical-consultancy"
                        className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                      >
                        Statistical Consultancy
                      </Link>
                      <Link
                        href="/services/survey-administration"
                        className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#f5a623] transition-colors"
                      >
                        Survey Administration
                      </Link>
                      <div className="border-t border-gray-200 my-2"></div>
                      <Link
                        href="/services/quote-request"
                        className="block px-4 py-3 text-sm text-[#f5a623] font-medium hover:bg-gray-100 transition-colors"
                      >
                        Request Quote
                      </Link>
                    </div>
                  </div>
                </div>
                <Link href="/about" className="text-[#0a2a5e] font-medium hover:text-[#f5a623] transition-colors">
                  About
                </Link>
                <Link href="/contact" className="text-[#0a2a5e] font-medium hover:text-[#f5a623] transition-colors">
                  Contact
                </Link>
                <Link href="/registration" className="text-[#0a2a5e] font-medium hover:text-[#f5a623] transition-colors">
                  Registration
                </Link>
                <button className="text-[#0a2a5e] hover:text-[#f5a623] transition-colors p-2" aria-label="Search">
                  <Search className="h-5 w-5" />
                </button>
              </div>

              {/* Mobile Menu Button */}
              <div className="lg:hidden">
                <button
                  className="text-[#0a2a5e] p-2 hover:text-[#f5a623] transition-colors"
                  aria-label="Toggle mobile menu"
                  onClick={() => {
                    const mobileMenu = document.getElementById('mobile-menu');
                    if (mobileMenu) {
                      mobileMenu.classList.toggle('hidden');
                    }
                  }}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Mobile Navigation Menu */}
            <div id="mobile-menu" className="hidden lg:hidden mt-4 pb-4 border-t border-gray-200">
              <div className="flex flex-col space-y-3 pt-4">
                <Link href="/" className="text-[#0a2a5e] font-medium py-2 px-3 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded">
                  Home
                </Link>
                <Link href="/services" className="text-[#0a2a5e] font-medium py-2 px-3 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded">
                  All Services
                </Link>
                <Link href="/services/medical-research-support" className="text-[#0a2a5e] text-sm py-2 px-6 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded">
                  Medical Research Support
                </Link>
                <Link href="/services/non-medical-research-support" className="text-[#0a2a5e] text-sm py-2 px-6 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded">
                  Non-Medical Research Support
                </Link>
                <Link href="/services/brain-tumor-consultation" className="text-[#0a2a5e] text-sm py-2 px-6 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded">
                  Brain Tumor Consultation
                </Link>
                <Link href="/services/statistical-consultancy" className="text-[#0a2a5e] text-sm py-2 px-6 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded">
                  Statistical Consultancy
                </Link>
                <Link href="/services/survey-administration" className="text-[#0a2a5e] text-sm py-2 px-6 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded">
                  Survey Administration
                </Link>
                <Link href="/services/quote-request" className="text-[#f5a623] font-medium py-2 px-6 hover:bg-gray-50 transition-colors rounded">
                  Request Quote
                </Link>
                <Link href="/about" className="text-[#0a2a5e] font-medium py-2 px-3 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded">
                  About
                </Link>
                <Link href="/contact" className="text-[#0a2a5e] font-medium py-2 px-3 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded">
                  Contact
                </Link>
                <Link href="/registration" className="text-[#0a2a5e] font-medium py-2 px-3 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded">
                  Registration
                </Link>
                <button className="text-[#0a2a5e] font-medium py-2 px-3 hover:bg-gray-50 hover:text-[#f5a623] transition-colors rounded text-left flex items-center">
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </button>
              </div>
            </div>
          </div>
        </nav>

        <Suspense>{children}</Suspense>

        <Footer />
      </body>
    </html>
  )
}
