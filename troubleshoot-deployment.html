<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deployment Troubleshooting Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0a2a5e;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #0a2a5e;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1a3a7e;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 MK Academia Deployment Troubleshooting</h1>
        
        <div class="info">
            <strong>Instructions:</strong> Upload this file to your shared hosting and access it via your browser to run deployment tests.
        </div>

        <div class="test-section">
            <h3>1. Server Environment Check</h3>
            <button onclick="checkServerInfo()">Check Server Info</button>
            <div id="server-info"></div>
        </div>

        <div class="test-section">
            <h3>2. File Structure Check</h3>
            <button onclick="checkFiles()">Check Required Files</button>
            <div id="file-check"></div>
        </div>

        <div class="test-section">
            <h3>3. URL Rewriting Test</h3>
            <button onclick="testRewriting()">Test .htaccess Rules</button>
            <div id="rewrite-test"></div>
        </div>

        <div class="test-section">
            <h3>4. Asset Loading Test</h3>
            <button onclick="testAssets()">Test CSS/JS Loading</button>
            <div id="asset-test"></div>
        </div>

        <div class="test-section">
            <h3>5. Navigation Test</h3>
            <button onclick="testNavigation()">Test Page Navigation</button>
            <div id="nav-test"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        function checkServerInfo() {
            const info = [];
            info.push(`<strong>User Agent:</strong> ${navigator.userAgent}`);
            info.push(`<strong>Current URL:</strong> ${window.location.href}`);
            info.push(`<strong>Protocol:</strong> ${window.location.protocol}`);
            info.push(`<strong>Host:</strong> ${window.location.host}`);
            
            // Check if running on HTTPS
            if (window.location.protocol === 'https:') {
                info.push(`<span class="success">✅ HTTPS is enabled</span>`);
            } else {
                info.push(`<span class="warning">⚠️ HTTP only (consider enabling HTTPS)</span>`);
            }

            showResult('server-info', info.join('<br>'), 'success');
        }

        function checkFiles() {
            const requiredFiles = [
                'index.html',
                'about.html',
                'contact.html',
                'services.html',
                'registration.html',
                '404.html',
                '_next/static/css/',
                '_next/static/chunks/'
            ];

            let results = [];
            let checkCount = 0;

            requiredFiles.forEach(file => {
                fetch(file, { method: 'HEAD' })
                    .then(response => {
                        checkCount++;
                        if (response.ok) {
                            results.push(`✅ ${file} - Found`);
                        } else {
                            results.push(`❌ ${file} - Missing (${response.status})`);
                        }
                        
                        if (checkCount === requiredFiles.length) {
                            showResult('file-check', results.join('<br>'), 'success');
                        }
                    })
                    .catch(error => {
                        checkCount++;
                        results.push(`❌ ${file} - Error: ${error.message}`);
                        
                        if (checkCount === requiredFiles.length) {
                            showResult('file-check', results.join('<br>'), 'error');
                        }
                    });
            });
        }

        function testRewriting() {
            const testUrls = [
                '/about',
                '/contact',
                '/services',
                '/registration'
            ];

            let results = [];
            let checkCount = 0;

            testUrls.forEach(url => {
                fetch(url, { method: 'HEAD' })
                    .then(response => {
                        checkCount++;
                        if (response.ok) {
                            results.push(`✅ ${url} - Rewrite working`);
                        } else {
                            results.push(`❌ ${url} - Rewrite failed (${response.status})`);
                        }
                        
                        if (checkCount === testUrls.length) {
                            const hasErrors = results.some(r => r.includes('❌'));
                            showResult('rewrite-test', results.join('<br>'), hasErrors ? 'error' : 'success');
                        }
                    })
                    .catch(error => {
                        checkCount++;
                        results.push(`❌ ${url} - Error: ${error.message}`);
                        
                        if (checkCount === testUrls.length) {
                            showResult('rewrite-test', results.join('<br>'), 'error');
                        }
                    });
            });
        }

        function testAssets() {
            // Test CSS loading
            const cssTest = document.createElement('link');
            cssTest.rel = 'stylesheet';
            cssTest.href = '_next/static/css/1be3c8427a142865.css';
            
            let results = [];
            
            cssTest.onload = function() {
                results.push('✅ CSS file loads correctly');
                checkJSAssets();
            };
            
            cssTest.onerror = function() {
                results.push('❌ CSS file failed to load');
                checkJSAssets();
            };
            
            document.head.appendChild(cssTest);
            
            function checkJSAssets() {
                fetch('_next/static/chunks/webpack-85b93d0195a685ae.js', { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            results.push('✅ JavaScript chunks accessible');
                        } else {
                            results.push('❌ JavaScript chunks not accessible');
                        }
                        
                        const hasErrors = results.some(r => r.includes('❌'));
                        showResult('asset-test', results.join('<br>'), hasErrors ? 'error' : 'success');
                    })
                    .catch(error => {
                        results.push(`❌ JavaScript test failed: ${error.message}`);
                        showResult('asset-test', results.join('<br>'), 'error');
                    });
            }
        }

        function testNavigation() {
            const testLinks = [
                { url: '/', name: 'Home' },
                { url: '/about', name: 'About' },
                { url: '/contact', name: 'Contact' },
                { url: '/services', name: 'Services' },
                { url: '/registration', name: 'Registration' }
            ];

            let results = [`<strong>Click these links to test navigation:</strong>`];
            
            testLinks.forEach(link => {
                results.push(`<a href="${link.url}" target="_blank" style="color: #0a2a5e; text-decoration: none; margin-right: 15px;">🔗 ${link.name}</a>`);
            });

            results.push('<br><strong>Manual Tests:</strong>');
            results.push('1. Click each link above - they should open correctly');
            results.push('2. Use browser back/forward buttons');
            results.push('3. Refresh pages to ensure they load');
            results.push('4. Check browser console (F12) for errors');

            showResult('nav-test', results.join('<br>'), 'success');
        }

        // Auto-run basic checks on page load
        window.onload = function() {
            checkServerInfo();
        };
    </script>
</body>
</html>
