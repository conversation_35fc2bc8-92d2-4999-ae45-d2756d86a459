export default function TestMobilePage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="max-w-2xl mx-auto text-center">
        <h1 className="text-3xl font-bold text-[#0a2a5e] mb-8">
          Mobile Navigation Test Page
        </h1>
        
        <div className="bg-white p-6 rounded-lg shadow-md border border-gray-100 mb-8">
          <h2 className="text-xl font-semibold text-[#0a2a5e] mb-4">
            Test Instructions
          </h2>
          <div className="text-left space-y-3 text-gray-700">
            <p><strong>Desktop (lg screens):</strong> Navigation should show horizontal menu with dropdown</p>
            <p><strong>Mobile (< lg screens):</strong> Navigation should show hamburger menu button</p>
            <p><strong>Mobile Menu Test:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Click hamburger menu button to open/close</li>
              <li>Menu should slide down smoothly</li>
              <li>All links should be clickable with proper touch targets</li>
              <li>Clicking a link should close the menu</li>
              <li>Pressing Escape should close the menu</li>
              <li>Clicking outside should close the menu</li>
            </ul>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-[#0a2a5e] mb-2">Screen Size</h3>
            <p className="text-sm text-gray-600">
              Current viewport: <span id="viewport-size">Loading...</span>
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-[#0a2a5e] mb-2">Touch Device</h3>
            <p className="text-sm text-gray-600">
              Touch support: <span id="touch-support">Loading...</span>
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-[#0a2a5e]">Quick Navigation Test</h3>
          <div className="flex flex-wrap gap-2 justify-center">
            <a href="/" className="bg-[#f5a623] hover:bg-[#e69816] text-white px-4 py-2 rounded transition-colors">
              Home
            </a>
            <a href="/services" className="bg-[#0a2a5e] hover:bg-[#1e3a8a] text-white px-4 py-2 rounded transition-colors">
              Services
            </a>
            <a href="/about" className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors">
              About
            </a>
            <a href="/contact" className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors">
              Contact
            </a>
          </div>
        </div>
      </div>

      <script dangerouslySetInnerHTML={{
        __html: `
          function updateViewportInfo() {
            const viewportSize = document.getElementById('viewport-size');
            const touchSupport = document.getElementById('touch-support');
            
            if (viewportSize) {
              viewportSize.textContent = window.innerWidth + 'x' + window.innerHeight;
            }
            
            if (touchSupport) {
              const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
              touchSupport.textContent = hasTouch ? 'Yes' : 'No';
            }
          }
          
          // Update on load and resize
          window.addEventListener('load', updateViewportInfo);
          window.addEventListener('resize', updateViewportInfo);
          
          // Initial update
          updateViewportInfo();
        `
      }} />
    </div>
  )
}
